#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TPDog 集合竞价数据获取工具

功能：
1. 获取集合竞价数据
2. 判断是否为交易日开放时间（09:15~09:30）
3. 生成成交量、主买入、涨幅排行榜
4. 保存数据到本地文件夹
5. 支持增量更新和强制更新模式
6. 支持测试参数（非开放时间返回样例数据）

作者：基于tpdog_industry_fund_flow.py模式开发
"""

import requests
import json
import time
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv
import pandas as pd

# 配置项定义
FORCE_UPDATE = False  # 强制更新开关，默认为False
MARKET_CLOSE_TIME = "15:00"  # 收盘时间定义
CALL_AUCTION_START = "09:15"  # 集合竞价开始时间
CALL_AUCTION_END = "09:30"  # 集合竞价结束时间
RECORD_FILE_NAME = "call_auction_fetch_records.json"  # 记录文件名
TEST_MODE = False  # 测试模式开关


def load_tpdog_token():
    """
    加载TPDog Token从环境变量
    """
    # 加载 .env 文件中的环境变量
    load_dotenv()

    # 获取TPDOG_TOKEN
    token = os.getenv("TPDOG_TOKEN")

    if not token:
        print("❌ 错误: 未在 .env 文件中找到 TPDOG_TOKEN")
        print("   请在项目根目录创建 .env 文件，并添加: TPDOG_TOKEN=你的token")
        return None

    print(f"✅ 成功加载TPDog Token: {token[:10]}...")
    return token


def is_trading_day(token, date=None):
    """
    检查指定日期是否为交易日
    
    参数:
    - token: TPDog API Token
    - date: 日期，格式 yyyy-MM-dd，默认为当前日期
    
    返回:
    - True: 是交易日
    - False: 不是交易日
    - None: 检查失败
    """
    if date is None:
        date = datetime.now().strftime('%Y-%m-%d')

    api_url = "https://www.tpdog.com/api/hs/trading_day/is"

    params = {
        'date': date,
        'token': token
    }

    try:
        print(f"🔍 正在检查 {date} 是否为交易日...")
        response = requests.get(api_url, params=params, timeout=10)
        response.raise_for_status()

        data = response.json()

        if data.get("code") == 1000:
            content = data.get("content", {})
            # 注意：API返回的字段名是 is_trainding（拼写错误是API本身的）
            is_trading = content.get("is_trainding", False)

            if is_trading:
                print(f"✅ {date} 是交易日")
            else:
                print(f"❌ {date} 不是交易日")

            return is_trading
        else:
            print(f"❌ 检查交易日失败: {data.get('message', '未知错误')}")
            return None

    except Exception as e:
        print(f"❌ 检查交易日失败: {e}")
        return None


def is_call_auction_time():
    """
    判断当前时间是否为集合竞价时间（09:15~09:30）
    """
    now = datetime.now()
    current_time = now.time()
    
    start_time = datetime.strptime(CALL_AUCTION_START, "%H:%M").time()
    end_time = datetime.strptime(CALL_AUCTION_END, "%H:%M").time()
    
    return start_time <= current_time <= end_time


def is_after_market_close():
    """
    判断当前时间是否为收盘后（15:00后）
    """
    now = datetime.now()
    market_close = datetime.strptime(MARKET_CLOSE_TIME, "%H:%M").time()
    current_time = now.time()

    return current_time > market_close


def get_date_folder(date=None):
    """
    获取指定日期文件夹路径
    """
    if date is None:
        date = datetime.now().strftime('%Y-%m-%d')
    return os.path.join("call_auction", date)


def get_previous_trading_day(token, date=None):
    """
    获取指定日期的上一个交易日
    """
    if date is None:
        date = datetime.now().strftime('%Y-%m-%d')
        
    current_date = datetime.strptime(date, '%Y-%m-%d')
    
    print(f"🔍 正在查找上一个交易日（从 {date} 开始）...")
    
    # 最多向前查找15个工作日
    for i in range(1, 16):
        previous_date = current_date - timedelta(days=i)
        previous_date_str = previous_date.strftime('%Y-%m-%d')
        
        # 检查是否为交易日
        is_trading = is_trading_day(token, previous_date_str)
        
        if is_trading is None:
            print(f"❌ 检查 {previous_date_str} 交易日状态失败")
            return None
            
        if is_trading:
            print(f"✅ 找到上一个交易日: {previous_date_str}")
            return previous_date_str
            
        # 控制请求频率
        time.sleep(0.1)
    
    print(f"❌ 在过去 15 天内未找到交易日")
    return None


def get_valid_trading_date(token, date=None):
    """
    获取有效的交易日期
    如果指定日期不是交易日，则返回上一个交易日
    """
    if date is None:
        date = datetime.now().strftime('%Y-%m-%d')
    
    print(f"🔍 检查日期 {date} 是否为交易日...")
    
    # 首先检查指定日期是否为交易日
    is_trading = is_trading_day(token, date)
    
    if is_trading is None:
        print(f"❌ 无法检查 {date} 是否为交易日")
        return None
    
    if is_trading:
        print(f"✅ {date} 是交易日，直接使用")
        return date
    
    # 如果不是交易日，查找上一个交易日
    print(f"⚠️ {date} 不是交易日，查找上一个交易日...")
    previous_trading_day = get_previous_trading_day(token, date)
    
    if previous_trading_day:
        print(f"✅ 将使用交易日: {previous_trading_day}")
        return previous_trading_day
    else:
        print(f"❌ 无法找到有效的交易日")
        return None


def load_fetch_records(date=None):
    """
    加载获取记录文件
    """
    if date is None:
        date = datetime.now().strftime('%Y-%m-%d')
        
    records_file = os.path.join(get_date_folder(date), RECORD_FILE_NAME)
    
    if os.path.exists(records_file):
        try:
            with open(records_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠️ 警告: 读取记录文件失败: {e}")
            return {}
    
    return {}


def save_fetch_records(records, date=None):
    """
    保存获取记录文件
    """
    if date is None:
        date = datetime.now().strftime('%Y-%m-%d')
        
    date_dir = get_date_folder(date)
    os.makedirs(date_dir, exist_ok=True)
    
    records_file = os.path.join(date_dir, RECORD_FILE_NAME)
    
    try:
        with open(records_file, 'w', encoding='utf-8') as f:
            json.dump(records, f, ensure_ascii=False, indent=2)
        print(f"📝 获取记录已更新: {records_file}")
    except Exception as e:
        print(f"❌ 保存记录文件失败: {e}")


def update_fetch_record(records, stock_code, stock_name, status, message=""):
    """
    更新单个股票的获取记录
    """
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    if stock_code not in records:
        records[stock_code] = {}
    
    records[stock_code].update({
        'stock_name': stock_name,
        'last_fetch_time': timestamp,
        'status': status,  # 'success', 'failed', 'skipped'
        'message': message
    })
    
    return records


def should_skip_stock(records, stock_code):
    """
    判断是否应该跳过该股票的数据获取
    - 如果开启强制更新，则不跳过
    - 如果是收盘时间后且本地已有数据，则跳过
    """
    if FORCE_UPDATE:
        return False
    
    if not is_after_market_close():
        return False
    
    # 检查是否已有今日记录
    if stock_code in records:
        last_status = records[stock_code].get('status')
        if last_status == 'success':
            return True
    
    return False


def get_call_auction_data(token, stock_code, sort=2, test_mode=False):
    """
    获取集合竞价数据

    参数:
    - token: TPDog API Token
    - stock_code: 股票代码，格式如 sh.600206
    - sort: 排序，1：按时间正序；2：按时间倒序
    - test_mode: 测试模式，t=1时，非指定调用时间返回样例数据
    """
    api_url = "http://www.tpdog.com/api/hs/current/call_auction"

    params = {
        'code': stock_code,
        'sort': sort,
        'token': token
    }

    # 如果是测试模式，添加测试参数
    if test_mode:
        params['t'] = 1

    try:
        print(f"🔄 正在获取 {stock_code} 集合竞价数据...")
        response = requests.get(api_url, params=params, timeout=10)
        response.raise_for_status()

        data = response.json()

        if data.get("code") == 1000:
            content = data.get("content", [])
            print(f"✅ 成功获取 {stock_code} 集合竞价数据: {len(content)} 条记录")
            return content
        else:
            error_message = data.get('message', '未知错误')
            print(f"❌ 获取{stock_code}集合竞价数据失败: {error_message}")

            # 如果是测试模式且返回示例数据信息，创建模拟数据
            if test_mode and "示例数据" in error_message:
                print(f"🧪 测试模式：生成 {stock_code} 的模拟集合竞价数据")
                return create_mock_auction_data(stock_code)

            return None

    except Exception as e:
        print(f"❌ 获取{stock_code}集合竞价数据失败: {e}")
        return None


def create_mock_auction_data(stock_code):
    """
    创建模拟集合竞价数据（用于测试模式）
    """
    import random

    # 股票名称映射
    stock_names = {
        "sh.600036": "招商银行",
        "sh.600519": "贵州茅台",
        "sh.600000": "浦发银行",
        "sz.000001": "平安银行",
        "sz.000002": "万科A",
        "sz.000858": "五粮液",
        "sh.600206": "有研新材",
        "sh.600276": "恒瑞医药",
        "sh.600887": "伊利股份",
        "sz.002415": "海康威视",
        "sz.300059": "东方财富",
        "sz.300750": "宁德时代",
        "sh.601318": "中国平安",
        "sh.601398": "工商银行",
        "sh.601857": "中国石油",
    }

    # 提取股票代码（去掉前缀）
    code_only = stock_code.split('.')[-1] if '.' in stock_code else stock_code
    stock_name = stock_names.get(stock_code, f"股票{code_only}")

    # 生成模拟数据
    current_time = datetime.now()
    mock_data = []

    # 生成3-8条集合竞价记录
    num_records = random.randint(3, 8)
    base_price = random.uniform(8.0, 50.0)  # 基础价格

    for i in range(num_records):
        # 时间在09:15-09:30之间
        minute_offset = random.randint(0, 15)
        second_offset = random.randint(0, 59)
        record_time = current_time.replace(hour=9, minute=15+minute_offset, second=second_offset)

        # 价格在基础价格附近波动
        price_change = random.uniform(-0.1, 0.1)
        price = round(base_price * (1 + price_change), 2)

        # 成交量
        volume = random.randint(10, 1000)

        # 涨跌幅（相对于假设的昨日收盘价）
        yesterday_close = base_price * 0.98  # 假设昨日收盘价
        rise_rate = round((price - yesterday_close) / yesterday_close, 4)

        mock_record = {
            "code": code_only,
            "name": stock_name,
            "time": record_time.strftime('%Y-%m-%d %H:%M:%S'),
            "price": price,
            "volume": volume,
            "rise_rate": rise_rate,
            "buy_sell": 0  # 集合竞价
        }

        mock_data.append(mock_record)

    return mock_data


def get_stock_list(token, market_type="sh"):
    """
    获取股票列表

    参数:
    - token: TPDog API Token
    - market_type: 交易所类型，sh：上证，sz：深证，bj：北证

    返回:
    - 股票列表，每个元素包含 code, name, type, req_code
    """
    api_url = "https://www.tpdog.com/api/hs/stocks/list"

    params = {
        'type': market_type,
        'token': token
    }

    try:
        print(f"🔄 正在获取 {market_type} 股票列表...")
        response = requests.get(api_url, params=params, timeout=30)
        response.raise_for_status()

        data = response.json()

        if data.get("code") == 1000:
            content = data.get("content", [])
            print(f"✅ 成功获取 {market_type} 股票列表: {len(content)} 只股票")
            return content
        else:
            print(f"❌ 获取{market_type}股票列表失败: {data.get('message', '未知错误')}")
            return []

    except Exception as e:
        print(f"❌ 获取{market_type}股票列表失败: {e}")
        return []


def get_all_stock_codes(token):
    """
    获取所有股票代码列表（上证+深证+北证）

    参数:
    - token: TPDog API Token

    返回:
    - 所有股票的req_code列表
    """
    all_stocks = []

    # 获取各个市场的股票
    markets = ["sh", "sz", "bj"]

    for market in markets:
        stocks = get_stock_list(token, market)
        if stocks:
            # 提取req_code
            req_codes = [stock.get('req_code') for stock in stocks if stock.get('req_code')]
            all_stocks.extend(req_codes)
            print(f"📊 {market} 市场股票数量: {len(req_codes)}")

        # 控制请求频率，避免触发限制（30次/秒）
        time.sleep(0.1)

    print(f"📈 总股票数量: {len(all_stocks)}")
    return all_stocks


def get_sample_stock_codes():
    """
    获取样本股票代码列表
    这里使用一些常见的股票代码作为示例
    """
    return [
        "sh.600036",  # 招商银行
        "sh.600519",  # 贵州茅台
        "sh.600000",  # 浦发银行
        "sz.000001",  # 平安银行
        "sz.000002",  # 万科A
        "sz.000858",  # 五粮液
        "sh.600206",  # 有研新材
        "sh.600276",  # 恒瑞医药
        "sh.600887",  # 伊利股份
        "sz.002415",  # 海康威视
        "sz.300059",  # 东方财富
        "sz.300750",  # 宁德时代
        "sh.601318",  # 中国平安
        "sh.601398",  # 工商银行
        "sh.601857",  # 中国石油
    ]


def get_all_call_auction_data(token, stock_codes=None, test_mode=False, use_all_stocks=False):
    """
    获取所有股票的集合竞价数据

    参数:
    - token: TPDog API Token
    - stock_codes: 股票代码列表，如果为None则使用默认样本
    - test_mode: 测试模式
    - use_all_stocks: 是否获取所有股票（从API获取完整股票列表）
    """
    if use_all_stocks:
        print(f"🔄 正在获取完整股票列表...")
        stock_codes = get_all_stock_codes(token)
        if not stock_codes:
            print("❌ 无法获取股票列表，使用默认样本")
            stock_codes = get_sample_stock_codes()
    elif stock_codes is None:
        stock_codes = get_sample_stock_codes()

    print(f"\n📊 开始获取集合竞价数据...")
    print(f"🔧 强制更新模式: {'开启' if FORCE_UPDATE else '关闭'}")
    print(f"🧪 测试模式: {'开启' if test_mode else '关闭'}")
    print(f"📈 使用完整股票列表: {'是' if use_all_stocks else '否'}")
    print(f"⏰ 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📈 是否为收盘后: {'是' if is_after_market_close() else '否'}")
    print(f"🕘 是否为集合竞价时间: {'是' if is_call_auction_time() else '否'}")

    # 加载获取记录
    records = load_fetch_records()

    all_data = []
    total_stocks = len(stock_codes)
    skipped_count = 0
    success_count = 0
    failed_count = 0

    for i, stock_code in enumerate(stock_codes, 1):
        # 检查是否应该跳过
        if should_skip_stock(records, stock_code):
            print(f"⏭️ 跳过 {stock_code} (已有数据)... ({i}/{total_stocks})")
            skipped_count += 1
            records = update_fetch_record(records, stock_code, stock_code, 'skipped', '已有数据，跳过获取')
            continue

        print(f"🔄 正在获取 {stock_code} 集合竞价数据... ({i}/{total_stocks})")

        auction_data = get_call_auction_data(token, stock_code, test_mode=test_mode)

        if auction_data:
            # 为每条记录添加股票代码信息
            for record in auction_data:
                record['stock_code'] = stock_code
            all_data.extend(auction_data)
            success_count += 1
            records = update_fetch_record(records, stock_code, stock_code, 'success', '数据获取成功')
        else:
            failed_count += 1
            records = update_fetch_record(records, stock_code, stock_code, 'failed', '数据获取失败')

        # 控制请求频率，避免触发限制
        if i < total_stocks:  # 最后一个请求不需要等待
            time.sleep(0.6)  # 等待600ms，确保不超过100次/分钟的限制

    # 保存获取记录
    save_fetch_records(records)

    print(f"\n📈 获取统计:")
    print(f"✅ 成功获取: {success_count} 只股票")
    print(f"⏭️ 跳过股票: {skipped_count} 只股票")
    print(f"❌ 获取失败: {failed_count} 只股票")
    print(f"📊 总计股票: {total_stocks} 只股票")
    print(f"📋 总计记录: {len(all_data)} 条集合竞价记录")

    return all_data


def continuous_call_auction_monitoring(token, use_all_stocks=False, test_mode=False):
    """
    在集合竞价时间内持续监控所有股票的集合竞价数据

    参数:
    - token: TPDog API Token
    - use_all_stocks: 是否获取所有股票（从API获取完整股票列表）
    - test_mode: 测试模式
    """
    print(f"\n🚀 开始集合竞价持续监控...")
    print(f"⏰ 监控时间: {CALL_AUCTION_START} ~ {CALL_AUCTION_END}")
    print(f"📈 使用完整股票列表: {'是' if use_all_stocks else '否'}")
    print(f"🧪 测试模式: {'开启' if test_mode else '关闭'}")

    # 获取股票列表
    if use_all_stocks:
        print(f"🔄 正在获取完整股票列表...")
        stock_codes = get_all_stock_codes(token)
        if not stock_codes:
            print("❌ 无法获取股票列表，使用默认样本")
            stock_codes = get_sample_stock_codes()
    else:
        stock_codes = get_sample_stock_codes()

    print(f"📊 将监控 {len(stock_codes)} 只股票")

    round_count = 0
    all_rounds_data = []

    # 在集合竞价时间内循环获取
    while test_mode or is_call_auction_time():
        round_count += 1
        current_time = datetime.now().strftime('%H:%M:%S')

        print(f"\n{'='*60}")
        print(f"🔄 第 {round_count} 轮数据获取 - {current_time}")
        print(f"{'='*60}")

        # 获取当前轮次的数据
        round_data = get_all_call_auction_data(
            token,
            stock_codes=stock_codes,
            test_mode=test_mode,
            use_all_stocks=False  # 已经获取过股票列表，不需要重复获取
        )

        if round_data:
            # 为每条记录添加轮次信息
            for record in round_data:
                record['round'] = round_count
                record['round_time'] = current_time

            all_rounds_data.extend(round_data)

            # 创建并保存当前轮次的排行榜
            df = create_call_auction_rankings(round_data)
            if df is not None:
                # 保存当前轮次数据
                current_date = datetime.now().strftime('%Y-%m-%d')
                save_call_auction_data(df, round_data, current_date)

        # 如果是测试模式，只运行一轮
        if test_mode:
            print(f"🧪 测试模式：完成一轮数据获取")
            break

        # 检查是否还在集合竞价时间内
        if not is_call_auction_time():
            print(f"⏰ 集合竞价时间结束，停止监控")
            break

        # 等待一段时间再进行下一轮
        print(f"⏳ 等待 30 秒后开始下一轮...")
        time.sleep(30)

    print(f"\n🏁 集合竞价监控结束")
    print(f"📊 总共完成 {round_count} 轮数据获取")
    print(f"📋 总计获取 {len(all_rounds_data)} 条记录")

    # 保存所有轮次的汇总数据
    if all_rounds_data:
        print(f"💾 正在保存汇总数据...")
        summary_df = create_call_auction_rankings(all_rounds_data)
        if summary_df is not None:
            current_date = datetime.now().strftime('%Y-%m-%d')
            timestamp = datetime.now().strftime('%H-%M')

            # 保存汇总数据
            date_dir = get_date_folder(current_date)
            os.makedirs(date_dir, exist_ok=True)

            summary_filename = f"{timestamp}_call_auction_summary_all_rounds.csv"
            summary_filepath = os.path.join(date_dir, summary_filename)
            summary_df.to_csv(summary_filepath, index=False, encoding='utf-8-sig')
            print(f"💾 汇总数据已保存到: {summary_filepath}")

            # 保存原始汇总数据
            raw_summary_filename = f"{timestamp}_call_auction_raw_summary_all_rounds.json"
            raw_summary_filepath = os.path.join(date_dir, raw_summary_filename)
            with open(raw_summary_filepath, 'w', encoding='utf-8') as f:
                json.dump({
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'total_rounds': round_count,
                    'total_records': len(all_rounds_data),
                    'monitoring_period': f"{CALL_AUCTION_START} ~ {CALL_AUCTION_END}",
                    'data': all_rounds_data
                }, f, ensure_ascii=False, indent=2)
            print(f"💾 原始汇总数据已保存到: {raw_summary_filepath}")

    return all_rounds_data


def create_call_auction_rankings(auction_data):
    """
    创建集合竞价排行榜

    参数:
    - auction_data: 所有股票的集合竞价数据列表
    """
    if not auction_data:
        print("❌ 无数据可供排序")
        return None

    # 转换为DataFrame便于排序
    df_data = []
    for record in auction_data:
        df_data.append({
            '股票代码': record.get('code', 'N/A'),
            '股票名称': record.get('name', 'N/A'),
            '时间': record.get('time', 'N/A'),
            '实时价格': record.get('price', 0),
            '成交量': record.get('volume', 0),
            '涨跌幅': record.get('rise_rate', 0),
            '主买卖': record.get('buy_sell', 0),
            '完整股票代码': record.get('stock_code', 'N/A')
        })

    df = pd.DataFrame(df_data)

    # 1. 成交量排行榜（前100名）
    print("\n" + "=" * 80)
    print("🏆 集合竞价成交量排行榜 TOP 100")
    print("=" * 80)

    volume_top100 = df.nlargest(100, '成交量')

    print(f"{'排名':<4} {'股票名称':<15} {'股票代码':<12} {'成交量':<10} {'价格':<8} {'涨跌幅':<8}")
    print("-" * 80)

    for idx, (_, row) in enumerate(volume_top100.iterrows(), 1):
        volume = row['成交量']
        price = row['实时价格']
        rise_rate = row['涨跌幅']

        # 根据涨跌幅选择显示颜色标识
        if rise_rate > 0:
            status = "📈"
        elif rise_rate < 0:
            status = "📉"
        else:
            status = "➖"

        print(f"{idx:<4} {row['股票名称']:<15} {row['股票代码']:<12} {volume:>8} {price:>6.2f} {status} {rise_rate:>6.2f}%")

    # 2. 主买入排行榜（前100名，buy_sell=1表示主买入）
    print("\n" + "=" * 80)
    print("💰 集合竞价主买入排行榜 TOP 100")
    print("=" * 80)

    # 筛选主买入数据（buy_sell=1）并按成交量排序
    main_buy_data = df[df['主买卖'] == 1] if len(df[df['主买卖'] == 1]) > 0 else df
    main_buy_top100 = main_buy_data.nlargest(100, '成交量')

    print(f"{'排名':<4} {'股票名称':<15} {'股票代码':<12} {'成交量':<10} {'价格':<8} {'涨跌幅':<8}")
    print("-" * 80)

    for idx, (_, row) in enumerate(main_buy_top100.iterrows(), 1):
        volume = row['成交量']
        price = row['实时价格']
        rise_rate = row['涨跌幅']

        if rise_rate > 0:
            status = "📈"
        elif rise_rate < 0:
            status = "📉"
        else:
            status = "➖"

        print(f"{idx:<4} {row['股票名称']:<15} {row['股票代码']:<12} {volume:>8} {price:>6.2f} {status} {rise_rate:>6.2f}%")

    # 3. 涨幅排行榜（前100名）
    print("\n" + "=" * 80)
    print("📈 集合竞价涨幅排行榜 TOP 100")
    print("=" * 80)

    rise_rate_top100 = df.nlargest(100, '涨跌幅')

    print(f"{'排名':<4} {'股票名称':<15} {'股票代码':<12} {'涨跌幅':<8} {'价格':<8} {'成交量':<10}")
    print("-" * 80)

    for idx, (_, row) in enumerate(rise_rate_top100.iterrows(), 1):
        volume = row['成交量']
        price = row['实时价格']
        rise_rate = row['涨跌幅']

        if rise_rate > 0:
            status = "📈"
        elif rise_rate < 0:
            status = "📉"
        else:
            status = "➖"

        print(f"{idx:<4} {row['股票名称']:<15} {row['股票代码']:<12} {status} {rise_rate:>6.2f}% {price:>6.2f} {volume:>8}")

    # 4. 统计摘要
    print("\n" + "=" * 80)
    print("📊 集合竞价统计摘要")
    print("=" * 80)

    total_records = len(df)
    positive_rise = len(df[df['涨跌幅'] > 0])
    negative_rise = len(df[df['涨跌幅'] < 0])
    flat_rise = len(df[df['涨跌幅'] == 0])

    total_volume = df['成交量'].sum()
    avg_price = df['实时价格'].mean()
    avg_rise_rate = df['涨跌幅'].mean()

    print(f"总记录数量: {total_records}")
    print(f"上涨股票: {positive_rise} ({positive_rise / total_records * 100:.1f}%)")
    print(f"下跌股票: {negative_rise} ({negative_rise / total_records * 100:.1f}%)")
    print(f"平盘股票: {flat_rise} ({flat_rise / total_records * 100:.1f}%)")
    print(f"总成交量: {total_volume:,}")
    print(f"平均价格: {avg_price:.2f} 元")
    print(f"平均涨跌幅: {avg_rise_rate:.2f}%")

    return df


def save_call_auction_data(df, auction_data, date=None):
    """
    保存集合竞价数据到本地文件

    参数:
    - df: 排行榜DataFrame
    - auction_data: 原始集合竞价数据
    - date: 日期，默认为当前日期
    """
    if date is None:
        date = datetime.now().strftime('%Y-%m-%d')

    now = datetime.now()
    timestamp = now.strftime('%H-%M')

    # 创建集合竞价文件夹和日期子文件夹
    date_dir = get_date_folder(date)
    os.makedirs(date_dir, exist_ok=True)

    try:
        # 1. 保存完整排行榜CSV文件
        rankings_filename = f"{timestamp}_call_auction_rankings.csv"
        rankings_filepath = os.path.join(date_dir, rankings_filename)
        df.to_csv(rankings_filepath, index=False, encoding='utf-8-sig')
        print(f"💾 完整排行榜已保存到: {rankings_filepath}")

        # 2. 保存成交量TOP100排行榜
        volume_top100 = df.nlargest(100, '成交量')
        volume_filename = f"{timestamp}_call_auction_volume_top100.csv"
        volume_filepath = os.path.join(date_dir, volume_filename)
        volume_top100.to_csv(volume_filepath, index=False, encoding='utf-8-sig')
        print(f"💾 成交量TOP100已保存到: {volume_filepath}")

        # 3. 保存主买入TOP100排行榜
        main_buy_data = df[df['主买卖'] == 1] if len(df[df['主买卖'] == 1]) > 0 else df
        main_buy_top100 = main_buy_data.nlargest(100, '成交量')
        main_buy_filename = f"{timestamp}_call_auction_main_buy_top100.csv"
        main_buy_filepath = os.path.join(date_dir, main_buy_filename)
        main_buy_top100.to_csv(main_buy_filepath, index=False, encoding='utf-8-sig')
        print(f"💾 主买入TOP100已保存到: {main_buy_filepath}")

        # 4. 保存涨幅TOP100排行榜
        rise_rate_top100 = df.nlargest(100, '涨跌幅')
        rise_rate_filename = f"{timestamp}_call_auction_rise_rate_top100.csv"
        rise_rate_filepath = os.path.join(date_dir, rise_rate_filename)
        rise_rate_top100.to_csv(rise_rate_filepath, index=False, encoding='utf-8-sig')
        print(f"💾 涨幅TOP100已保存到: {rise_rate_filepath}")

        # 5. 保存原始数据JSON文件
        raw_data_filename = f"{timestamp}_call_auction_raw_data.json"
        raw_data_filepath = os.path.join(date_dir, raw_data_filename)
        with open(raw_data_filepath, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': now.strftime('%Y-%m-%d %H:%M:%S'),
                'total_records': len(auction_data),
                'data': auction_data
            }, f, ensure_ascii=False, indent=2)
        print(f"💾 原始数据已保存到: {raw_data_filepath}")

        # 6. 保存统计摘要文件
        summary_filename = f"{timestamp}_call_auction_summary.txt"
        summary_filepath = os.path.join(date_dir, summary_filename)
        save_summary_to_file(df, summary_filepath)
        print(f"💾 统计摘要已保存到: {summary_filepath}")

        print(f"\n📁 所有文件已保存到目录: {date_dir}")

    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
        import traceback
        traceback.print_exc()


def save_summary_to_file(df, filepath):
    """
    保存统计摘要到文本文件
    """
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"TPDog 集合竞价数据统计摘要\n")
            f.write("=" * 50 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"强制更新: {'开启' if FORCE_UPDATE else '关闭'}\n")
            f.write(f"测试模式: {'开启' if TEST_MODE else '关闭'}\n\n")

            total_records = len(df)
            positive_rise = len(df[df['涨跌幅'] > 0])
            negative_rise = len(df[df['涨跌幅'] < 0])
            flat_rise = len(df[df['涨跌幅'] == 0])

            total_volume = df['成交量'].sum()
            avg_price = df['实时价格'].mean()
            avg_rise_rate = df['涨跌幅'].mean()

            f.write(f"总记录数量: {total_records}\n")
            f.write(f"上涨股票: {positive_rise} ({positive_rise / total_records * 100:.1f}%)\n")
            f.write(f"下跌股票: {negative_rise} ({negative_rise / total_records * 100:.1f}%)\n")
            f.write(f"平盘股票: {flat_rise} ({flat_rise / total_records * 100:.1f}%)\n")
            f.write(f"总成交量: {total_volume:,}\n")
            f.write(f"平均价格: {avg_price:.2f} 元\n")
            f.write(f"平均涨跌幅: {avg_rise_rate:.2f}%\n\n")

            # 添加前10名成交量和前10名涨幅
            f.write("成交量前10名:\n")
            f.write("-" * 30 + "\n")
            volume_top10 = df.nlargest(10, '成交量')
            for idx, (_, row) in enumerate(volume_top10.iterrows(), 1):
                f.write(f"{idx:2d}. {row['股票名称']:<15} {row['成交量']:>8}\n")

            f.write("\n涨幅前10名:\n")
            f.write("-" * 30 + "\n")
            rise_top10 = df.nlargest(10, '涨跌幅')
            for idx, (_, row) in enumerate(rise_top10.iterrows(), 1):
                f.write(f"{idx:2d}. {row['股票名称']:<15} {row['涨跌幅']:>6.2f}%\n")

    except Exception as e:
        print(f"❌ 保存统计摘要失败: {e}")


def print_single_stock_auction(data):
    """
    格式化打印单个股票的集合竞价数据
    """
    if not data:
        print("❌ 无有效数据可显示")
        return

    print("\n" + "=" * 50)
    print("📊 TPDog 集合竞价数据")
    print("=" * 50)

    for record in data:
        print(f"股票代码: {record.get('code', 'N/A')}")
        print(f"股票名称: {record.get('name', 'N/A')}")
        print(f"时间: {record.get('time', 'N/A')}")
        print(f"实时价格: {record.get('price', 0):.2f} 元")
        print(f"成交量: {record.get('volume', 0):,}")
        print(f"涨跌幅: {record.get('rise_rate', 0):.2f}%")
        print(f"主买卖: {record.get('buy_sell', 0)} (0:集合竞价)")
        print("-" * 50)

    print("=" * 50)


def main():
    """
    主函数
    """
    global FORCE_UPDATE, TEST_MODE

    print("🚀 TPDog 集合竞价数据获取工具启动...")
    print(f"🔧 强制更新模式: {'开启' if FORCE_UPDATE else '关闭'}")
    print(f"🧪 测试模式: {'开启' if TEST_MODE else '关闭'}")

    # 1. 加载Token
    token = load_tpdog_token()
    if not token:
        return

    # 2. 检查当前是否为交易日
    current_date = datetime.now().strftime('%Y-%m-%d')
    valid_date = get_valid_trading_date(token, current_date)

    if not valid_date:
        print("❌ 无法确定有效的交易日，程序终止")
        return

    # 3. 检查是否为集合竞价时间或测试模式
    is_auction_time = is_call_auction_time()

    if not is_auction_time and not TEST_MODE:
        print(f"⚠️ 当前时间不是集合竞价时间 ({CALL_AUCTION_START}~{CALL_AUCTION_END})")
        print("💡 提示: 可以开启测试模式获取样例数据")

        try:
            choice = input("是否开启测试模式？(y/N): ").strip().lower()
            if choice == 'y':
                TEST_MODE = True
                print("🧪 测试模式已开启")
            else:
                print("👋 程序退出")
                return
        except KeyboardInterrupt:
            print("\n👋 程序已退出")
            return

    # 4. 选择运行模式
    print("\n请选择运行模式:")
    print("1. 获取单个股票集合竞价数据")
    print("2. 获取所有样本股票集合竞价排行榜")
    print("3. 获取所有股票集合竞价排行榜（完整股票列表）")
    print("4. 集合竞价时间持续监控（样本股票）")
    print("5. 集合竞价时间持续监控（所有股票）")
    print("6. 切换强制更新模式")
    print("7. 切换测试模式")

    try:
        choice = input("请输入选择 (1-7): ").strip()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
        return

    if choice == "1":
        # 单个股票查询
        try:
            stock_code = input("请输入股票代码 (如 sh.600206): ").strip()
            if not stock_code:
                stock_code = "sh.600206"  # 默认股票

            print(f"\n📊 正在获取 {stock_code} 集合竞价数据...")
            data = get_call_auction_data(token, stock_code, test_mode=TEST_MODE)
            if data:
                print_single_stock_auction(data)
        except KeyboardInterrupt:
            print("\n👋 已取消操作")

    elif choice == "2":
        # 批量获取样本股票数据
        print(f"\n📋 正在获取所有样本股票集合竞价数据...")

        auction_data = get_all_call_auction_data(token, test_mode=TEST_MODE, use_all_stocks=False)

        if auction_data:
            # 创建并显示排行榜
            df = create_call_auction_rankings(auction_data)

            # 保存数据到文件
            if df is not None:
                save_call_auction_data(df, auction_data, valid_date)
        else:
            print("❌ 未获取到有效的集合竞价数据")

    elif choice == "3":
        # 批量获取所有股票数据（完整股票列表）
        print(f"\n📋 正在获取所有股票集合竞价数据（完整列表）...")
        print("⚠️ 注意: 这将获取所有上市股票数据，可能需要较长时间")

        try:
            confirm = input("确认继续？(y/N): ").strip().lower()
            if confirm != 'y':
                print("👋 已取消操作")
                return
        except KeyboardInterrupt:
            print("\n👋 已取消操作")
            return

        auction_data = get_all_call_auction_data(token, test_mode=TEST_MODE, use_all_stocks=True)

        if auction_data:
            # 创建并显示排行榜
            df = create_call_auction_rankings(auction_data)

            # 保存数据到文件
            if df is not None:
                save_call_auction_data(df, auction_data, valid_date)
        else:
            print("❌ 未获取到有效的集合竞价数据")

    elif choice == "4":
        # 集合竞价时间持续监控（样本股票）
        print(f"\n🔄 开始集合竞价持续监控（样本股票）...")

        if not TEST_MODE and not is_call_auction_time():
            print(f"⚠️ 当前不是集合竞价时间 ({CALL_AUCTION_START}~{CALL_AUCTION_END})")
            print("💡 提示: 可以开启测试模式进行测试")
            try:
                confirm = input("是否开启测试模式继续？(y/N): ").strip().lower()
                if confirm == 'y':
                    TEST_MODE = True
                    print("🧪 测试模式已开启")
                else:
                    print("👋 已取消操作")
                    return
            except KeyboardInterrupt:
                print("\n👋 已取消操作")
                return

        continuous_call_auction_monitoring(token, use_all_stocks=False, test_mode=TEST_MODE)

    elif choice == "5":
        # 集合竞价时间持续监控（所有股票）
        print(f"\n🔄 开始集合竞价持续监控（所有股票）...")
        print("⚠️ 注意: 这将持续监控所有上市股票，数据量巨大")

        if not TEST_MODE and not is_call_auction_time():
            print(f"⚠️ 当前不是集合竞价时间 ({CALL_AUCTION_START}~{CALL_AUCTION_END})")
            print("💡 提示: 可以开启测试模式进行测试")
            try:
                confirm = input("是否开启测试模式继续？(y/N): ").strip().lower()
                if confirm == 'y':
                    TEST_MODE = True
                    print("🧪 测试模式已开启")
                else:
                    print("👋 已取消操作")
                    return
            except KeyboardInterrupt:
                print("\n👋 已取消操作")
                return

        try:
            confirm = input("确认开始监控所有股票？(y/N): ").strip().lower()
            if confirm != 'y':
                print("👋 已取消操作")
                return
        except KeyboardInterrupt:
            print("\n👋 已取消操作")
            return

        continuous_call_auction_monitoring(token, use_all_stocks=True, test_mode=TEST_MODE)

    elif choice == "6":
        # 切换强制更新模式
        current_mode = FORCE_UPDATE
        FORCE_UPDATE = not current_mode
        print(f"🔧 强制更新模式已{'开启' if FORCE_UPDATE else '关闭'}")
        print("⚠️ 注意: 此设置仅在当前运行中生效，重启程序后恢复默认设置")
        # 返回主菜单
        main()
        return

    elif choice == "7":
        # 切换测试模式
        current_mode = TEST_MODE
        TEST_MODE = not current_mode
        print(f"🧪 测试模式已{'开启' if TEST_MODE else '关闭'}")
        print("⚠️ 注意: 此设置仅在当前运行中生效，重启程序后恢复默认设置")
        # 返回主菜单
        main()
        return

    else:
        print("❌ 无效选择，程序退出")
        return

    print(f"\n{'=' * 50}")
    print("💡 提示:")
    print("   1. 数据来源于TPDog API，开放时间为交易日09:15~09:30")
    print("   2. 请确保在 .env 文件中正确设置了 TPDOG_TOKEN")
    print("   3. API限制:")
    print("      - 股票列表API: 30次/秒")
    print("      - 集合竞价API: 100次/分钟")
    print("      - 程序已自动控制请求频率")
    print("   4. 所有数据已按日期保存到 call_auction 文件夹中")
    print("   5. 保存的文件包括:")
    print("      - 完整排行榜数据 (CSV)")
    print("      - 成交量TOP100 (CSV)")
    print("      - 主买入TOP100 (CSV)")
    print("      - 涨幅TOP100 (CSV)")
    print("      - 原始数据 (JSON)")
    print("      - 统计摘要 (TXT)")
    print("      - 获取记录 (JSON)")
    print("      - 持续监控汇总数据 (CSV/JSON)")
    print(f"   6. 强制更新模式: {'开启' if FORCE_UPDATE else '关闭'}")
    print(f"   7. 测试模式: {'开启' if TEST_MODE else '关闭'}")
    print("   8. 收盘后(15:00后)会自动跳过已获取的股票数据")
    print("   9. 每次运行都会记录获取状态和时间")
    print("  10. 非集合竞价时间可开启测试模式获取样例数据")
    print("  11. 持续监控模式会在集合竞价时间内循环获取数据")
    print("  12. 获取所有股票数据需要较长时间，请耐心等待")
    print("  13. 持续监控会每30秒进行一轮完整的数据获取")


if __name__ == "__main__":
    main()
