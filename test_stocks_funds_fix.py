#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试版块个股资金流接口修复
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 导入修复后的函数
from gainian.tpdog_sector_monitor import get_tpdog_sector_stocks_funds, TPDOG_TOKEN

def test_sector_stocks_funds():
    """测试版块个股资金流获取"""
    print("🧪 测试版块个股资金流接口修复")
    print("=" * 50)
    
    if not TPDOG_TOKEN:
        print("❌ 未配置TPDOG_TOKEN")
        return
    
    # 测试概念版块 - PCB概念
    print("\n🔄 测试PCB概念版块个股资金流获取...")
    pcb_funds_df = get_tpdog_sector_stocks_funds('bkc.881109', TPDOG_TOKEN)
    if pcb_funds_df is not None and not pcb_funds_df.empty:
        print(f"✅ PCB概念个股资金流获取成功: {len(pcb_funds_df)} 只股票")

        # 按主力流入排序并显示前5名
        pcb_funds_df = pcb_funds_df.sort_values('income', ascending=False)
        print("\n📈 PCB概念个股资金流前5名:")
        top_5 = pcb_funds_df.head(5)
        for idx, row in top_5.iterrows():
            income_str = f"{row['income']/10000:.2f}万" if abs(row['income']) < 100000000 else f"{row['income']/100000000:.2f}亿"
            print(f"  {idx+1}. {row['name']:10s} | 主力流入: {income_str:>10s}")
    else:
        print("❌ PCB概念个股资金流获取失败")

    # 测试英文文件名生成
    from gainian.tpdog_sector_monitor import get_english_sector_name
    print("\n🔄 测试英文文件名生成...")
    test_names = ['PCB', '3D玻璃', '人工智能', '新能源', '生物制品']
    for name in test_names:
        english_name = get_english_sector_name(name)
        print(f"  {name} -> {english_name}")
    
    print("\n" + "=" * 50)
    print("🧪 测试完成")

if __name__ == "__main__":
    test_sector_stocks_funds()
