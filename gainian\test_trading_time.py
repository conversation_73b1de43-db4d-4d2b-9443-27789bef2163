#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试交易时间判断逻辑
验证程序在不同时间点的行为
"""

from datetime import datetime, time
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入交易时间判断函数
from tpdog_sector_monitor import is_trading_time, TRADING_START_1, TRADING_END_1, TRADING_START_2, TRADING_END_2

def test_specific_time(test_time_str, expected_result):
    """测试特定时间点"""
    # 直接使用时间对象进行判断
    test_hour, test_minute = map(int, test_time_str.split(':'))
    test_time_obj = time(test_hour, test_minute)

    # 直接使用交易时间判断逻辑
    result = (TRADING_START_1 <= test_time_obj <= TRADING_END_1) or (TRADING_START_2 <= test_time_obj <= TRADING_END_2)

    status = "✅" if result == expected_result else "❌"
    print(f"{status} {test_time_str} - 预期: {'交易时间' if expected_result else '非交易时间'}, 实际: {'交易时间' if result else '非交易时间'}")
    return result == expected_result

def main():
    """主测试函数"""
    print("=" * 60)
    print("🕐 TPDOG交易时间判断逻辑测试")
    print("=" * 60)
    print(f"交易时间配置:")
    print(f"  上午: {TRADING_START_1.strftime('%H:%M')} - {TRADING_END_1.strftime('%H:%M')}")
    print(f"  下午: {TRADING_START_2.strftime('%H:%M')} - {TRADING_END_2.strftime('%H:%M')}")
    print("=" * 60)
    
    # 测试用例
    test_cases = [
        # 格式: (时间, 是否应该是交易时间)
        ("09:00", False),  # 开盘前
        ("09:29", False),  # 开盘前1分钟
        ("09:30", True),   # 开盘时间
        ("09:31", True),   # 开盘后1分钟
        ("10:00", True),   # 上午交易时间
        ("11:00", True),   # 上午交易时间
        ("11:29", True),   # 上午收盘前1分钟
        ("11:30", True),   # 上午收盘时间
        ("11:31", False),  # 上午收盘后1分钟
        ("12:00", False),  # 午休时间
        ("12:59", False),  # 下午开盘前1分钟
        ("13:00", True),   # 下午开盘时间
        ("13:01", True),   # 下午开盘后1分钟
        ("14:00", True),   # 下午交易时间
        ("14:59", True),   # 下午收盘前1分钟
        ("15:00", True),   # 下午收盘时间
        ("15:01", False),  # 下午收盘后1分钟
        ("16:00", False),  # 收盘后
        ("20:00", False),  # 晚上
    ]
    
    print("\n📋 测试结果:")
    print("-" * 60)
    
    passed = 0
    total = len(test_cases)
    
    for time_str, expected in test_cases:
        if test_specific_time(time_str, expected):
            passed += 1
    
    print("-" * 60)
    print(f"📊 测试总结: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！交易时间判断逻辑正确")
    else:
        print("⚠️ 部分测试失败，请检查交易时间判断逻辑")
    
    print("\n" + "=" * 60)
    print("🔍 关键时间点分析:")
    print("=" * 60)
    print("✅ 11:30 - 上午收盘时间，应该仍在交易时间内")
    print("❌ 11:31 - 上午收盘后，应该暂停数据获取")
    print("❌ 12:59 - 下午开盘前，应该仍在暂停状态")
    print("✅ 13:00 - 下午开盘时间，应该恢复数据获取")
    print("✅ 15:00 - 下午收盘时间，应该仍在交易时间内")
    print("❌ 15:01 - 下午收盘后，应该停止数据获取")
    
    print("\n📝 程序行为说明:")
    print("- 每2分钟执行一次 get_sector_funds_data()")
    print("- 该函数内部会调用 is_trading_time() 判断")
    print("- 如果非交易时间，会打印 '⚠️ 非交易时间，跳过版块资金流获取' 并返回")
    print("- 如果是交易时间，会正常获取和保存数据")
    print("- 因此程序会在11:30后自动暂停，在13:00后自动恢复")

if __name__ == "__main__":
    main()
