import os
import requests
import json
import time
from datetime import datetime
from dotenv import load_dotenv
import pandas as pd


def load_tpdog_token():
    """
    加载TPDog Token从环境变量
    """
    # 加载 .env 文件中的环境变量
    load_dotenv()

    # 获取TPDOG_TOKEN
    token = os.getenv("TPDOG_TOKEN")

    if not token:
        print("❌ 错误: 未在 .env 文件中找到 TPDOG_TOKEN")
        print("   请在项目根目录创建 .env 文件，并添加: TPDOG_TOKEN=你的token")
        return None

    print(f"✅ 成功加载TPDog Token: {token[:10]}...")
    return token


def get_stock_list(token, exchange_type="sz"):
    """
    获取股票列表
    
    参数:
    - token: TPDog API Token
    - exchange_type: 交易所类型 (sh: 上证, sz: 深证, bj: 北证)
    
    返回:
    - 股票列表数据
    """
    api_url = "https://www.tpdog.com/api/hs/stocks/list"
    
    params = {
        'type': exchange_type,
        'token': token
    }
    
    try:
        response = requests.get(api_url, params=params, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        
        if data.get("code") == 1000:
            content = data.get("content", [])
            print(f"✅ 成功获取 {exchange_type} 股票列表: {len(content)} 只股票")
            return content
        else:
            print(f"❌ 获取股票列表失败: {data.get('message', '未知错误')}")
            return None
            
    except Exception as e:
        print(f"❌ 获取股票列表失败: {e}")
        return None


def get_stock_board_info(token, stock_code):
    """
    获取股票的板块信息（F10板块）
    
    参数:
    - token: TPDog API Token
    - stock_code: 股票代码，格式如 sh.600206
    
    返回:
    - 板块信息列表
    """
    api_url = "https://www.tpdog.com/api/hs/f10/board"
    
    params = {
        'code': stock_code,
        'token': token
    }
    
    try:
        response = requests.get(api_url, params=params, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        
        if data.get("code") == 1000:
            content = data.get("content", [])
            return content
        else:
            print(f"❌ 获取股票 {stock_code} 板块信息失败: {data.get('message', '未知错误')}")
            return None
            
    except Exception as e:
        print(f"❌ 获取股票 {stock_code} 板块信息失败: {e}")
        return None


def print_stock_board_info(stock_code, board_info):
    """
    打印股票的板块信息
    """
    if not board_info:
        print(f"❌ 股票 {stock_code} 无板块信息")
        return
    
    print(f"\n📊 股票 {stock_code} 的板块信息:")
    print("=" * 60)
    
    # 按板块类型分类
    concept_boards = []  # 概念板块
    industry_boards = []  # 行业板块
    region_boards = []   # 地域板块
    
    for board in board_info:
        board_type = board.get('type', '')
        if '概念' in board_type:
            concept_boards.append(board)
        elif '行业' in board_type:
            industry_boards.append(board)
        elif '地域' in board_type:
            region_boards.append(board)
    
    # 打印概念板块
    if concept_boards:
        print(f"\n🔥 概念板块 ({len(concept_boards)}个):")
        for i, board in enumerate(concept_boards, 1):
            print(f"  {i}. {board.get('name', 'N/A')} (代码: {board.get('code', 'N/A')})")
    
    # 打印行业板块
    if industry_boards:
        print(f"\n🏭 行业板块 ({len(industry_boards)}个):")
        for i, board in enumerate(industry_boards, 1):
            print(f"  {i}. {board.get('name', 'N/A')} (代码: {board.get('code', 'N/A')})")
    
    # 打印地域板块
    if region_boards:
        print(f"\n🌍 地域板块 ({len(region_boards)}个):")
        for i, board in enumerate(region_boards, 1):
            print(f"  {i}. {board.get('name', 'N/A')} (代码: {board.get('code', 'N/A')})")


def get_single_stock_info(token, stock_code):
    """
    获取单个股票的概念和板块信息并打印
    """
    print(f"\n🔍 正在查询股票 {stock_code} 的板块信息...")
    
    board_info = get_stock_board_info(token, stock_code)
    if board_info:
        print_stock_board_info(stock_code, board_info)
        return board_info
    else:
        print(f"❌ 未能获取股票 {stock_code} 的板块信息")
        return None


def save_all_stocks_board_info(token):
    """
    获取所有股票的概念和板块信息，并保存到本地文件
    """
    print("\n🚀 开始获取所有股票的概念和板块信息...")
    
    # 创建保存目录
    base_dir = "py"
    concept_dir = os.path.join(base_dir, "concept")
    industry_dir = os.path.join(base_dir, "industry")
    
    os.makedirs(concept_dir, exist_ok=True)
    os.makedirs(industry_dir, exist_ok=True)
    
    # 获取所有交易所的股票列表
    all_stocks = []
    exchanges = ["sh", "sz", "bj"]
    
    for exchange in exchanges:
        print(f"\n📋 获取 {exchange} 交易所股票列表...")
        stocks = get_stock_list(token, exchange)
        if stocks:
            all_stocks.extend(stocks)
            time.sleep(0.1)  # 控制请求频率
    
    if not all_stocks:
        print("❌ 未能获取到股票列表")
        return
    
    print(f"✅ 总共获取到 {len(all_stocks)} 只股票")
    
    # 用于存储概念和行业信息
    concept_stocks = {}  # {概念名称: [股票列表]}
    industry_stocks = {}  # {行业名称: [股票列表]}
    
    # 统计信息
    success_count = 0
    failed_count = 0
    total_count = len(all_stocks)
    
    print(f"\n🔄 开始获取每只股票的板块信息...")
    
    for i, stock in enumerate(all_stocks, 1):
        stock_code = stock.get('req_code', '')
        stock_name = stock.get('name', '')
        
        if not stock_code:
            continue
            
        print(f"📊 ({i}/{total_count}) 正在处理: {stock_name} ({stock_code})")
        
        board_info = get_stock_board_info(token, stock_code)
        
        if board_info:
            success_count += 1
            
            # 处理板块信息
            for board in board_info:
                board_name = board.get('name', '')
                board_type = board.get('type', '')
                
                stock_info = {
                    'code': stock.get('code', ''),
                    'name': stock_name,
                    'req_code': stock_code,
                    'exchange': stock.get('type', '')
                }
                
                # 分类存储
                if '概念' in board_type and board_name:
                    if board_name not in concept_stocks:
                        concept_stocks[board_name] = []
                    concept_stocks[board_name].append(stock_info)
                
                elif '行业' in board_type and board_name:
                    if board_name not in industry_stocks:
                        industry_stocks[board_name] = []
                    industry_stocks[board_name].append(stock_info)
        else:
            failed_count += 1
        
        # 控制请求频率，避免触发限制
        time.sleep(0.1)
        
        # 每处理100只股票显示一次进度
        if i % 100 == 0:
            print(f"📈 进度: {i}/{total_count} ({i/total_count*100:.1f}%) - 成功: {success_count}, 失败: {failed_count}")
    
    print(f"\n📊 处理完成!")
    print(f"✅ 成功处理: {success_count} 只股票")
    print(f"❌ 处理失败: {failed_count} 只股票")
    print(f"🔥 发现概念: {len(concept_stocks)} 个")
    print(f"🏭 发现行业: {len(industry_stocks)} 个")
    
    # 保存概念信息
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 保存概念数据
    concept_file = os.path.join(concept_dir, f"stock_concepts_{timestamp}.json")
    with open(concept_file, 'w', encoding='utf-8') as f:
        json.dump({
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'total_concepts': len(concept_stocks),
            'total_stocks_processed': success_count,
            'concepts': concept_stocks
        }, f, ensure_ascii=False, indent=2)
    
    print(f"💾 概念数据已保存到: {concept_file}")
    
    # 保存行业数据
    industry_file = os.path.join(industry_dir, f"stock_industries_{timestamp}.json")
    with open(industry_file, 'w', encoding='utf-8') as f:
        json.dump({
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'total_industries': len(industry_stocks),
            'total_stocks_processed': success_count,
            'industries': industry_stocks
        }, f, ensure_ascii=False, indent=2)
    
    print(f"💾 行业数据已保存到: {industry_file}")
    
    # 生成概念统计报告
    concept_stats_file = os.path.join(concept_dir, f"concept_stats_{timestamp}.txt")
    with open(concept_stats_file, 'w', encoding='utf-8') as f:
        f.write(f"股票概念统计报告\n")
        f.write("=" * 50 + "\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"处理股票总数: {success_count}\n")
        f.write(f"概念总数: {len(concept_stocks)}\n\n")

        # 按股票数量排序
        sorted_concepts = sorted(concept_stocks.items(), key=lambda x: len(x[1]), reverse=True)

        f.write("概念板块排行榜 (按股票数量):\n")
        f.write("-" * 40 + "\n")
        for i, (concept_name, stocks) in enumerate(sorted_concepts, 1):
            f.write(f"{i:3d}. {concept_name:<20} {len(stocks):>4} 只股票\n")

    print(f"📊 概念统计报告已保存到: {concept_stats_file}")

    # 生成行业统计报告
    industry_stats_file = os.path.join(industry_dir, f"industry_stats_{timestamp}.txt")
    with open(industry_stats_file, 'w', encoding='utf-8') as f:
        f.write(f"股票行业统计报告\n")
        f.write("=" * 50 + "\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"处理股票总数: {success_count}\n")
        f.write(f"行业总数: {len(industry_stocks)}\n\n")

        # 按股票数量排序
        sorted_industries = sorted(industry_stocks.items(), key=lambda x: len(x[1]), reverse=True)

        f.write("行业板块排行榜 (按股票数量):\n")
        f.write("-" * 40 + "\n")
        for i, (industry_name, stocks) in enumerate(sorted_industries, 1):
            f.write(f"{i:3d}. {industry_name:<20} {len(stocks):>4} 只股票\n")

    print(f"📊 行业统计报告已保存到: {industry_stats_file}")

    return concept_stocks, industry_stocks


def main():
    """
    主函数
    """
    print("🚀 TPDog 股票概念和板块查询工具启动...")

    # 1. 加载Token
    token = load_tpdog_token()
    if not token:
        return

    # 2. 选择运行模式
    print("\n请选择运行模式:")
    print("1. 查询单个股票的概念和板块")
    print("2. 获取所有股票的概念和板块信息并保存")

    try:
        choice = input("请输入选择 (1-2): ").strip()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
        return

    if choice == "1":
        # 单个股票查询
        try:
            stock_code = input("请输入股票代码 (格式如 sh.600206 或 sz.000001): ").strip()
            if stock_code:
                get_single_stock_info(token, stock_code)
            else:
                print("❌ 股票代码不能为空")
        except KeyboardInterrupt:
            print("\n👋 程序已退出")

    elif choice == "2":
        # 获取所有股票信息
        try:
            confirm = input("⚠️ 此操作将获取所有股票的板块信息，可能需要较长时间，确认继续？(y/N): ").strip().lower()
            if confirm == 'y':
                save_all_stocks_board_info(token)
            else:
                print("⏭️ 已取消操作")
        except KeyboardInterrupt:
            print("\n👋 程序已退出")

    else:
        print("❌ 无效选择")
        return

    print(f"\n{'=' * 50}")
    print("💡 提示:")
    print("   1. 数据来源于TPDog API")
    print("   2. 请确保在 .env 文件中正确设置了 TPDOG_TOKEN")
    print("   3. API限制为30次/秒，程序已自动控制请求频率")
    print("   4. 概念和行业数据保存在 py/concept 和 py/industry 文件夹中")
    print("   5. 生成的文件包括:")
    print("      - JSON数据文件 (详细的股票-概念/行业映射)")
    print("      - 统计报告 (TXT格式，按股票数量排序)")


if __name__ == "__main__":
    main()
