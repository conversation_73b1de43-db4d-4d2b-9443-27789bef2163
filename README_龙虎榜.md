# TPDog 龙虎榜数据获取工具

这是一个基于TPDog API的龙虎榜数据获取工具，参考了 `tpdog_industry_fund_flow.py` 的设计模式。

## 功能特性

### 🐉 龙虎榜数据获取
- ✅ 获取指定日期的龙虎榜数据
- ✅ 自动检查交易日，非交易日获取上一个交易日数据
- ✅ 显示龙虎榜数据摘要和排行榜
- ✅ 支持数据打印和文件保存

### 📈 个股龙虎榜历史
- ✅ 获取指定股票的龙虎榜历史数据
- ✅ 支持日期范围查询
- ✅ 显示历史上榜记录和统计信息
- ✅ 按股票代码保存历史数据

### 🔄 增量更新机制
- ✅ 收盘后(15:00后)自动跳过已获取的数据
- ✅ 本地文件检查，避免重复获取
- ✅ 获取记录追踪，记录每次获取的状态和时间
- ✅ 强制更新开关，可覆盖增量更新逻辑

### 📁 数据保存
- ✅ 按日期分类保存到 `dragon_tiger_board` 文件夹
- ✅ 当日龙虎榜数据保存为JSON格式
- ✅ 个股历史数据按股票代码命名保存
- ✅ 获取记录文件追踪所有操作

## 文件结构

```
dragon_tiger_board/
├── 2024-07-12/                          # 日期文件夹
│   ├── 09-30_dragon_tiger_board_2024-07-12.json    # 当日龙虎榜数据
│   ├── 000001_dragon_tiger_history.json            # 个股历史数据
│   ├── 000002_dragon_tiger_history.json
│   └── dragon_tiger_fetch_records.json             # 获取记录
└── 2024-07-11/
    └── ...
```

## 安装依赖

```bash
pip install requests python-dotenv pandas
```

## 环境配置

在项目根目录创建 `.env` 文件：

```env
TPDOG_TOKEN=你的TPDog_API_Token
```

## 使用方法

### 1. 直接运行主程序

```bash
python tpdog_dragon_tiger_board.py
```

程序提供6种运行模式：
1. 获取当日龙虎榜数据
2. 获取指定日期龙虎榜数据  
3. 获取当日龙虎榜 + 所有股票历史数据
4. 获取指定股票龙虎榜历史
5. 查看获取记录
6. 切换强制更新模式

### 2. 作为模块使用

```python
from tpdog_dragon_tiger_board import (
    load_tpdog_token,
    get_dragon_tiger_board,
    get_stock_dragon_tiger_history,
    print_dragon_tiger_summary
)

# 加载Token
token = load_tpdog_token()

# 获取当日龙虎榜
data = get_dragon_tiger_board(token, "2024-07-12")
print_dragon_tiger_summary(data)

# 获取个股历史
history = get_stock_dragon_tiger_history(token, "sz.000001", "2024-07-01", "2024-07-12")
```

### 3. 运行演示脚本

```bash
python demo_dragon_tiger.py
```

## API接口说明

### 龙虎榜接口
- **URL**: `https://www.tpdog.com/api/hs/board/bill`
- **参数**: `date` (日期), `token` (API Token)
- **频率限制**: 30次/秒

### 个股龙虎榜历史接口  
- **URL**: `https://www.tpdog.com/api/hs/board/bill_info`
- **参数**: `start` (开始日期), `end` (结束日期), `code` (股票代码), `token`
- **频率限制**: 30次/秒

### 交易日查询接口
- **URL**: `https://www.tpdog.com/api/trading_day/is`
- **参数**: `date` (日期), `token`

## 配置选项

在脚本顶部可以修改以下配置：

```python
FORCE_UPDATE = False          # 强制更新开关
MARKET_CLOSE_TIME = "15:00"   # 收盘时间
RECORD_FILE_NAME = "dragon_tiger_fetch_records.json"  # 记录文件名
DRAGON_TIGER_FOLDER = "dragon_tiger_board"            # 数据文件夹
```

## 增量更新逻辑

1. **交易日检查**: 自动检查是否为交易日，非交易日获取上一个交易日数据
2. **时间判断**: 收盘后(15:00后)启用增量更新逻辑
3. **本地检查**: 检查本地是否已有相同日期的数据文件
4. **记录追踪**: 记录每次获取的状态、时间和结果
5. **强制更新**: 可通过开关强制重新获取所有数据

## 数据格式

### 龙虎榜数据字段
- `code`: 股票代码
- `name`: 股票名称  
- `date`: 日期
- `close`: 收盘价
- `rise_rate`: 涨跌幅
- `net_amt`: 龙虎榜净买入额
- `buy_amt`: 龙虎榜买入额
- `sell_amt`: 龙虎榜卖出额
- `reason`: 上榜原因
- `buys`: 买五详情
- `sells`: 卖五详情

## 注意事项

1. **API限制**: 请求频率不超过30次/秒，程序已自动控制
2. **Token安全**: 请妥善保管TPDog API Token
3. **数据准确性**: 数据来源于TPDog API，请以官方数据为准
4. **存储空间**: 长期运行会积累大量数据文件，注意磁盘空间
5. **网络稳定**: 确保网络连接稳定，避免数据获取中断

## 错误处理

程序包含完善的错误处理机制：
- 网络请求超时重试
- API响应状态检查
- 文件读写异常处理
- 用户输入验证
- 详细的错误日志记录

## 更新日志

- v1.0.0: 初始版本，实现基本龙虎榜数据获取功能
- 支持当日龙虎榜和个股历史数据获取
- 实现增量更新和数据保存功能
- 添加交易日检查和自动日期处理
