#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的版块成分股缓存机制和全市场资金流缓存机制
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 导入修复后的函数
from gainian.tpdog_sector_monitor import (
    get_tpdog_sector_stocks_funds,
    get_cached_sector_stocks,
    get_sector_stocks_cache_file,
    TPDOG_TOKEN
)

def test_optimized_cache_mechanism():
    """测试优化后的缓存机制"""
    print("🧪 测试优化后的版块成分股缓存机制和全市场资金流缓存机制")
    print("=" * 80)
    
    if not TPDOG_TOKEN:
        print("❌ 未配置TPDOG_TOKEN")
        return
    
    # 测试多个版块，验证全市场资金流缓存效果
    test_sectors = [
        ('bkc.881109', 'PCB概念'),
        ('bki.880297', '电子元件'),
        ('bki.880319', '通信设备')
    ]
    
    print(f"\n🔄 测试多个版块的缓存机制...")
    print(f"📋 测试版块: {', '.join([f'{name}({code})' for code, name in test_sectors])}")
    
    total_start_time = __import__('time').time()
    
    for i, (sector_code, sector_name) in enumerate(test_sectors):
        print(f"\n{'='*50}")
        print(f"📊 第{i+1}个版块: {sector_name} ({sector_code})")
        print(f"{'='*50}")
        
        # 检查成分股缓存
        cached_stocks = get_cached_sector_stocks(sector_code)
        if cached_stocks is not None:
            print(f"✅ 发现成分股缓存: {len(cached_stocks)} 只股票")
        else:
            print("📋 未发现成分股缓存，将首次获取")
        
        # 获取版块个股资金流
        start_time = __import__('time').time()
        funds_df = get_tpdog_sector_stocks_funds(sector_code, TPDOG_TOKEN)
        elapsed_time = __import__('time').time() - start_time
        
        if funds_df is not None:
            print(f"✅ 获取成功: {len(funds_df)} 只股票，耗时: {elapsed_time:.2f}秒")
            
            # 显示前3名个股资金流
            if not funds_df.empty:
                funds_df_sorted = funds_df.sort_values('income', ascending=False)
                print(f"📈 {sector_name} 个股资金流前3名:")
                for idx, row in funds_df_sorted.head(3).iterrows():
                    income_str = f"{row['income']/10000:.2f}万" if abs(row['income']) < 100000000 else f"{row['income']/100000000:.2f}亿"
                    print(f"  {idx+1}. {row['name']:10s} | 主力流入: {income_str:>10s}")
        else:
            print(f"❌ 获取失败")
    
    total_elapsed_time = __import__('time').time() - total_start_time
    print(f"\n{'='*80}")
    print(f"🎯 总耗时: {total_elapsed_time:.2f}秒")
    print(f"📊 平均每个版块耗时: {total_elapsed_time/len(test_sectors):.2f}秒")
    
    # 测试第二轮，验证缓存效果
    print(f"\n🔄 第二轮测试（验证缓存效果）...")
    second_round_start_time = __import__('time').time()
    
    for i, (sector_code, sector_name) in enumerate(test_sectors):
        print(f"\n📊 第二轮第{i+1}个版块: {sector_name}")
        
        start_time = __import__('time').time()
        funds_df = get_tpdog_sector_stocks_funds(sector_code, TPDOG_TOKEN)
        elapsed_time = __import__('time').time() - start_time
        
        if funds_df is not None:
            print(f"✅ 获取成功: {len(funds_df)} 只股票，耗时: {elapsed_time:.2f}秒")
        else:
            print(f"❌ 获取失败")
    
    second_round_elapsed_time = __import__('time').time() - second_round_start_time
    print(f"\n{'='*80}")
    print(f"🎯 第二轮总耗时: {second_round_elapsed_time:.2f}秒")
    print(f"📊 第二轮平均每个版块耗时: {second_round_elapsed_time/len(test_sectors):.2f}秒")
    
    # 计算缓存效果
    if second_round_elapsed_time < total_elapsed_time:
        improvement = ((total_elapsed_time - second_round_elapsed_time) / total_elapsed_time * 100)
        print(f"🚀 缓存机制生效！第二轮速度提升 {improvement:.1f}%")
    else:
        print(f"⚠️ 第二轮获取时间未明显减少")
    
    print("\n" + "=" * 80)
    print("🧪 缓存机制测试完成")

if __name__ == "__main__":
    test_optimized_cache_mechanism()
