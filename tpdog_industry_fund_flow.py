import os
import requests
import json
import time
from datetime import datetime
from dotenv import load_dotenv
import pandas as pd

# 配置项定义
FORCE_UPDATE = False  # 强制更新开关，默认为False
MARKET_CLOSE_TIME = "15:00"  # 收盘时间定义
RECORD_FILE_NAME = "fetch_records.json"  # 记录文件名
CACHE_FILE_NAME = "sector_cache.json"  # 缓存文件名


def load_tpdog_token():
    """
    加载TPDog Token从环境变量
    """
    # 加载 .env 文件中的环境变量
    load_dotenv()

    # 获取TPDOG_TOKEN
    token = os.getenv("TPDOG_TOKEN")

    if not token:
        print("❌ 错误: 未在 .env 文件中找到 TPDOG_TOKEN")
        print("   请在项目根目录创建 .env 文件，并添加: TPDOG_TOKEN=你的token")
        return None

    print(f"✅ 成功加载TPDog Token: {token[:10]}...")
    return token


def is_after_market_close():
    """
    判断当前时间是否为收盘后（15:00后）
    """
    now = datetime.now()
    market_close = datetime.strptime(MARKET_CLOSE_TIME, "%H:%M").time()
    current_time = now.time()

    return current_time > market_close


def get_date_folder():
    """
    获取当前日期文件夹路径
    """
    date_folder = datetime.now().strftime('%Y-%m-%d')
    return os.path.join("fund_flow", date_folder)


def load_sector_cache():
    """
    加载版块数据缓存
    """
    cache_file = os.path.join(get_date_folder(), CACHE_FILE_NAME)

    if os.path.exists(cache_file):
        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
                print(f"✅ 成功加载缓存文件: {cache_file}")
                return cache_data
        except Exception as e:
            print(f"⚠️ 警告: 读取缓存文件失败: {e}")
            return {}

    print(f"📝 未找到缓存文件，将创建新的缓存")
    return {}


def save_sector_cache(cache_data):
    """
    保存版块数据缓存
    """
    date_dir = get_date_folder()
    os.makedirs(date_dir, exist_ok=True)

    cache_file = os.path.join(date_dir, CACHE_FILE_NAME)

    try:
        with open(cache_file, 'w', encoding='utf-8') as f:
            json.dump(cache_data, f, ensure_ascii=False, indent=2)
        print(f"💾 缓存数据已保存: {cache_file}")
    except Exception as e:
        print(f"❌ 保存缓存文件失败: {e}")


def is_sector_cached(cache_data, sector_type):
    """
    检查指定类型的版块数据是否已缓存

    参数:
    - cache_data: 缓存数据
    - sector_type: 版块类型 (bki/bkc/bkr)
    """
    today = datetime.now().strftime('%Y-%m-%d')

    if sector_type in cache_data:
        cache_date = cache_data[sector_type].get('cache_date')
        if cache_date == today:
            print(f"🔍 发现 {sector_type} 版块今日缓存数据")
            return True

    return False


def get_cached_sectors(cache_data, sector_type):
    """
    从缓存中获取版块列表
    """
    if sector_type in cache_data:
        return cache_data[sector_type].get('sectors', [])
    return []


def cache_sectors(cache_data, sector_type, sectors):
    """
    缓存版块列表数据
    """
    today = datetime.now().strftime('%Y-%m-%d')
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    cache_data[sector_type] = {
        'cache_date': today,
        'cache_time': timestamp,
        'sectors': sectors,
        'total_count': len(sectors)
    }

    return cache_data


def load_fetch_records():
    """
    加载获取记录文件
    """
    records_file = os.path.join(get_date_folder(), RECORD_FILE_NAME)

    if os.path.exists(records_file):
        try:
            with open(records_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠️ 警告: 读取记录文件失败: {e}")
            return {}

    return {}


def save_fetch_records(records):
    """
    保存获取记录文件
    """
    date_dir = get_date_folder()
    os.makedirs(date_dir, exist_ok=True)

    records_file = os.path.join(date_dir, RECORD_FILE_NAME)

    try:
        with open(records_file, 'w', encoding='utf-8') as f:
            json.dump(records, f, ensure_ascii=False, indent=2)
        print(f"📝 获取记录已更新: {records_file}")
    except Exception as e:
        print(f"❌ 保存记录文件失败: {e}")


def update_sector_record(records, sector_code, sector_name, status, message=""):
    """
    更新单个版块的记录
    """
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    if sector_code not in records:
        records[sector_code] = {}

    records[sector_code].update({
        'sector_name': sector_name,
        'last_fetch_time': timestamp,
        'status': status,  # 'success', 'failed', 'skipped'
        'message': message
    })

    return records


def should_skip_sector(records, sector_code):
    """
    判断是否应该跳过该版块的数据获取
    - 如果开启强制更新，则不跳过
    - 如果是收盘时间后且本地已有数据，则跳过
    """
    if FORCE_UPDATE:
        return False

    if not is_after_market_close():
        return False

    # 检查是否已有今日记录
    if sector_code in records:
        last_status = records[sector_code].get('status')
        if last_status == 'success':
            return True

    return False


def check_existing_files(sector_type_name):
    """
    检查是否已有当日的数据文件
    """
    date_dir = get_date_folder()

    if not os.path.exists(date_dir):
        return False

    # 检查是否有相关文件
    files = os.listdir(date_dir)
    pattern = f"_tpdog_{sector_type_name}_"

    return any(pattern in filename for filename in files)


def get_sector_list(token, sector_type="bki"):
    """
    获取版块列表（带缓存功能）

    参数:
    - token: TPDog API Token
    - sector_type: 版块类型
      - bkr: 地域版块
      - bkc: 概念版块
      - bki: 行业版块
    """
    # 加载缓存
    cache_data = load_sector_cache()

    # 检查是否有缓存且非强制更新
    if not FORCE_UPDATE and is_sector_cached(cache_data, sector_type):
        cached_sectors = get_cached_sectors(cache_data, sector_type)
        print(f"📋 使用缓存的{sector_type}版块列表 (共{len(cached_sectors)}个)")
        return cached_sectors

    # 从API获取数据
    api_url = "https://www.tpdog.com/api/bk/list"

    params = {
        'type': sector_type,
        'token': token
    }

    try:
        print(f"🔄 正在从API获取{sector_type}版块列表...")
        response = requests.get(api_url, params=params, timeout=10)
        response.raise_for_status()

        data = response.json()

        if data.get("code") == 1000:
            content = data.get("content", [])
            print(f"✅ 成功获取 {len(content)} 个版块")

            # 缓存数据
            cache_data = cache_sectors(cache_data, sector_type, content)
            save_sector_cache(cache_data)

            return content
        else:
            print(f"❌ 获取版块列表失败: {data.get('message', '未知错误')}")
            return []

    except Exception as e:
        print(f"❌ 获取版块列表失败: {e}")
        return []


def get_fund_flow_data(token, code, date=None, period=1):
    """
    获取TPDog资金流数据（支持行业、概念、地域版块）

    参数:
    - token: TPDog API Token
    - code: 版块代码，格式如 bki.880115, bkc.880664, bkr.881669
    - date: 日期，格式 yyyy-MM-dd，默认为当前日期
    - period: 周期数，默认为1，取值范围[1-10]
    """

    # 如果没有指定日期，使用当前日期
    if date is None:
        date = datetime.now().strftime('%Y-%m-%d')

    # 根据版块类型选择不同的API
    if code.startswith('bki.'):
        api_url = "https://www.tpdog.com/api/hs/fund/industry"
    elif code.startswith('bkc.'):
        api_url = "https://www.tpdog.com/api/hs/fund/concept"
    elif code.startswith('bkr.'):
        api_url = "https://www.tpdog.com/api/hs/fund/region"
    else:
        print(f"❌ 不支持的版块代码格式: {code}")
        return None

    # 设置请求参数
    params = {
        'code': code,
        'date': date,
        'period': period,
        'token': token
    }

    try:
        # 发送GET请求
        response = requests.get(api_url, params=params, timeout=10)
        response.raise_for_status()

        # 解析JSON响应
        data = response.json()

        # 检查API响应状态
        if data.get("code") == 1000:
            return data.get("content", {})
        else:
            print(f"❌ 获取{code}资金流失败: {data.get('message', '未知错误')}")
            return None

    except Exception as e:
        print(f"❌ 获取{code}资金流失败: {e}")
        return None


def get_industry_fund_flow(token, code, date=None, period=1):
    """
    获取TPDog行业资金流数据（保持向后兼容）
    """
    return get_fund_flow_data(token, code, date, period)


def get_all_sectors_fund_flow(token, sectors, date=None):
    """
    获取所有版块的资金流数据

    参数:
    - token: TPDog API Token
    - sectors: 版块列表
    - date: 日期
    """
    print(f"\n📊 开始获取所有版块资金流数据...")
    print(f"🔧 强制更新模式: {'开启' if FORCE_UPDATE else '关闭'}")
    print(f"⏰ 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📈 是否为收盘后: {'是' if is_after_market_close() else '否'}")

    # 加载获取记录
    records = load_fetch_records()

    all_data = []
    total_sectors = len(sectors)
    skipped_count = 0
    success_count = 0
    failed_count = 0

    for i, sector in enumerate(sectors, 1):
        req_code = sector.get('req_code')
        sector_name = sector.get('name')

        # 检查是否应该跳过
        if should_skip_sector(records, req_code):
            print(f"⏭️ 跳过 {sector_name} (已有数据)... ({i}/{total_sectors})")
            skipped_count += 1
            records = update_sector_record(records, req_code, sector_name, 'skipped', '已有数据，跳过获取')
            continue

        print(f"🔄 正在获取 {sector_name} 资金流数据... ({i}/{total_sectors})")

        fund_data = get_fund_flow_data(token, req_code, date)

        if fund_data:
            # 添加版块名称到数据中
            fund_data['sector_name'] = sector_name
            fund_data['req_code'] = req_code
            all_data.append(fund_data)
            success_count += 1
            records = update_sector_record(records, req_code, sector_name, 'success', '数据获取成功')
        else:
            failed_count += 1
            records = update_sector_record(records, req_code, sector_name, 'failed', '数据获取失败')

        # 控制请求频率，避免触发限制
        if i < total_sectors:  # 最后一个请求不需要等待
            time.sleep(0.1)  # 等待100ms，确保不超过30次/秒的限制

    # 保存获取记录
    save_fetch_records(records)

    print(f"\n📈 获取统计:")
    print(f"✅ 成功获取: {success_count} 个版块")
    print(f"⏭️ 跳过版块: {skipped_count} 个版块")
    print(f"❌ 获取失败: {failed_count} 个版块")
    print(f"📊 总计版块: {total_sectors} 个版块")

    return all_data


def create_rankings(fund_flow_data):
    """
    创建排行榜

    参数:
    - fund_flow_data: 所有版块的资金流数据列表
    """
    if not fund_flow_data:
        print("❌ 无数据可供排序")
        return

    # 转换为DataFrame便于排序
    df_data = []
    for data in fund_flow_data:
        df_data.append({
            '版块名称': data.get('sector_name', 'N/A'),
            '版块代码': data.get('code', 'N/A'),
            '主力净流入': data.get('m_net', 0),
            '主力流入': data.get('m_in', 0),
            '主力流出': data.get('m_out', 0),
            '主力流入比例': data.get('m_in_ratio', 0),
            '主力流出比例': data.get('m_out_ratio', 0),
            '散户净流入': data.get('r_net', 0),
            '统计日期': data.get('start', 'N/A')
        })

    df = pd.DataFrame(df_data)

    # 1. 主力净流入排行榜（前100名）
    print("\n" + "=" * 80)
    print("🏆 主力资金净流入排行榜 TOP 100")
    print("=" * 80)

    net_inflow_top100 = df.nlargest(100, '主力净流入')

    print(f"{'排名':<4} {'版块名称':<20} {'主力净流入(元)':<15} {'流入比例':<8} {'流出比例':<8}")
    print("-" * 80)

    for idx, (_, row) in enumerate(net_inflow_top100.iterrows(), 1):
        net_inflow = row['主力净流入']
        in_ratio = row['主力流入比例']
        out_ratio = row['主力流出比例']

        # 根据净流入金额选择显示颜色标识
        if net_inflow > 0:
            status = "✅"
        else:
            status = "❌"

        print(f"{idx:<4} {row['版块名称']:<20} {status} {net_inflow:>12,.0f} {in_ratio:>6.2f}% {out_ratio:>6.2f}%")

    # 2. 主力流入比例排行榜（前100名）
    print("\n" + "=" * 80)
    print("📈 主力资金流入比例排行榜 TOP 100")
    print("=" * 80)

    inflow_ratio_top100 = df.nlargest(100, '主力流入比例')

    print(f"{'排名':<4} {'版块名称':<20} {'流入比例':<8} {'主力净流入(元)':<15} {'流出比例':<8}")
    print("-" * 80)

    for idx, (_, row) in enumerate(inflow_ratio_top100.iterrows(), 1):
        net_inflow = row['主力净流入']
        in_ratio = row['主力流入比例']
        out_ratio = row['主力流出比例']

        if net_inflow > 0:
            status = "✅"
        else:
            status = "❌"

        print(f"{idx:<4} {row['版块名称']:<20} {in_ratio:>6.2f}% {status} {net_inflow:>12,.0f} {out_ratio:>6.2f}%")

    # 3. 统计摘要
    print("\n" + "=" * 80)
    print("📊 统计摘要")
    print("=" * 80)

    total_sectors = len(df)
    positive_net_inflow = len(df[df['主力净流入'] > 0])
    negative_net_inflow = len(df[df['主力净流入'] < 0])

    total_net_inflow = df['主力净流入'].sum()
    avg_inflow_ratio = df['主力流入比例'].mean()
    avg_outflow_ratio = df['主力流出比例'].mean()

    print(f"总版块数量: {total_sectors}")
    print(f"主力净流入版块: {positive_net_inflow} ({positive_net_inflow / total_sectors * 100:.1f}%)")
    print(f"主力净流出版块: {negative_net_inflow} ({negative_net_inflow / total_sectors * 100:.1f}%)")
    print(f"全市场主力净流入总额: {total_net_inflow:,.0f} 元")
    print(f"平均主力流入比例: {avg_inflow_ratio:.2f}%")
    print(f"平均主力流出比例: {avg_outflow_ratio:.2f}%")

    return df


def save_raw_sector_data(sector_type_name, sectors, fund_flow_data):
    """
    保存原始版块数据到本地
    """
    now = datetime.now()
    date_folder = now.strftime('%Y-%m-%d')
    timestamp = now.strftime('%H-%M')

    # 创建原始数据目录
    raw_data_dir = os.path.join("fund_flow", date_folder, "raw_data")
    os.makedirs(raw_data_dir, exist_ok=True)

    try:
        # 1. 保存版块列表原始数据
        sectors_filename = f"{timestamp}_{sector_type_name}_sectors_raw.json"
        sectors_filepath = os.path.join(raw_data_dir, sectors_filename)
        with open(sectors_filepath, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': now.strftime('%Y-%m-%d %H:%M:%S'),
                'sector_type': sector_type_name,
                'total_count': len(sectors),
                'sectors': sectors
            }, f, ensure_ascii=False, indent=2)
        print(f"💾 版块列表原始数据已保存: {sectors_filepath}")

        # 2. 保存资金流原始数据
        fund_flow_filename = f"{timestamp}_{sector_type_name}_fund_flow_raw.json"
        fund_flow_filepath = os.path.join(raw_data_dir, fund_flow_filename)
        with open(fund_flow_filepath, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': now.strftime('%Y-%m-%d %H:%M:%S'),
                'sector_type': sector_type_name,
                'total_count': len(fund_flow_data),
                'fund_flow_data': fund_flow_data
            }, f, ensure_ascii=False, indent=2)
        print(f"💾 资金流原始数据已保存: {fund_flow_filepath}")

        # 3. 保存数据汇总信息
        summary_filename = f"{timestamp}_{sector_type_name}_data_summary.json"
        summary_filepath = os.path.join(raw_data_dir, summary_filename)

        summary_data = {
            'timestamp': now.strftime('%Y-%m-%d %H:%M:%S'),
            'sector_type': sector_type_name,
            'data_summary': {
                'total_sectors': len(sectors),
                'successful_fund_flow': len(fund_flow_data),
                'success_rate': f"{len(fund_flow_data) / len(sectors) * 100:.1f}%" if sectors else "0%"
            },
            'files': {
                'sectors_file': sectors_filename,
                'fund_flow_file': fund_flow_filename
            }
        }

        with open(summary_filepath, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, ensure_ascii=False, indent=2)
        print(f"💾 数据汇总信息已保存: {summary_filepath}")

        print(f"📁 原始数据已保存到目录: {raw_data_dir}")

    except Exception as e:
        print(f"❌ 保存原始数据失败: {e}")
        import traceback
        traceback.print_exc()


def save_data_to_files(df, fund_flow_data, sector_type_name, sectors=None):
    """
    保存数据到fund_flow文件夹，按日期分类保存
    """
    now = datetime.now()
    date_folder = now.strftime('%Y-%m-%d')
    timestamp = now.strftime('%H-%M')

    # 创建fund_flow文件夹和日期子文件夹
    fund_flow_dir = "fund_flow"
    date_dir = os.path.join(fund_flow_dir, date_folder)
    os.makedirs(date_dir, exist_ok=True)

    try:
        # 1. 保存排行榜CSV文件
        rankings_filename = f"{timestamp}_tpdog_{sector_type_name}_rankings.csv"
        rankings_filepath = os.path.join(date_dir, rankings_filename)
        df.to_csv(rankings_filepath, index=False, encoding='utf-8-sig')
        print(f"💾 排行榜数据已保存到: {rankings_filepath}")

        # 2. 保存主力净流入TOP100排行榜单独文件
        net_inflow_top100 = df.nlargest(100, '主力净流入')
        net_inflow_filename = f"{timestamp}_tpdog_{sector_type_name}_net_inflow_top100.csv"
        net_inflow_filepath = os.path.join(date_dir, net_inflow_filename)
        net_inflow_top100.to_csv(net_inflow_filepath, index=False, encoding='utf-8-sig')
        print(f"💾 净流入TOP100已保存到: {net_inflow_filepath}")

        # 3. 保存主力流入比例TOP100排行榜单独文件
        inflow_ratio_top100 = df.nlargest(100, '主力流入比例')
        inflow_ratio_filename = f"{timestamp}_tpdog_{sector_type_name}_inflow_ratio_top100.csv"
        inflow_ratio_filepath = os.path.join(date_dir, inflow_ratio_filename)
        inflow_ratio_top100.to_csv(inflow_ratio_filepath, index=False, encoding='utf-8-sig')
        print(f"💾 流入比例TOP100已保存到: {inflow_ratio_filepath}")

        # 4. 保存所有板块原始数据JSON文件（兼容旧版本）
        raw_data_filename = f"{timestamp}_tpdog_{sector_type_name}_raw_data.json"
        raw_data_filepath = os.path.join(date_dir, raw_data_filename)
        with open(raw_data_filepath, 'w', encoding='utf-8') as f:
            json.dump(fund_flow_data, f, ensure_ascii=False, indent=2)
        print(f"💾 原始数据已保存到: {raw_data_filepath}")

        # 5. 保存统计摘要文件
        try:
            summary_filename = f"{timestamp}_tpdog_{sector_type_name}_summary.txt"
            summary_filepath = os.path.join(date_dir, summary_filename)
            save_summary_to_file(df, summary_filepath, sector_type_name)
            print(f"💾 统计摘要已保存到: {summary_filepath}")
        except Exception as summary_error:
            print(f"❌ 保存统计摘要失败: {summary_error}")
            import traceback
            traceback.print_exc()

        # 6. 保存原始数据到专门目录（新增功能）
        try:
            if sectors:
                save_raw_sector_data(sector_type_name, sectors, fund_flow_data)
        except Exception as raw_data_error:
            print(f"❌ 保存原始数据失败: {raw_data_error}")
            import traceback
            traceback.print_exc()

        print(f"\n📁 所有文件已保存到目录: {date_dir}")

    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
        import traceback
        traceback.print_exc()


def save_summary_to_file(df, filepath, sector_type_name):
    """
    保存统计摘要到文本文件
    """
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"TPDog {sector_type_name}资金流统计摘要\n")
            f.write("=" * 50 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"强制更新: {'开启' if FORCE_UPDATE else '关闭'}\n\n")

            total_sectors = len(df)
            positive_net_inflow = len(df[df['主力净流入'] > 0])
            negative_net_inflow = len(df[df['主力净流入'] < 0])

            total_net_inflow = df['主力净流入'].sum()
            avg_inflow_ratio = df['主力流入比例'].mean()
            avg_outflow_ratio = df['主力流出比例'].mean()

            f.write(f"总版块数量: {total_sectors}\n")
            f.write(f"主力净流入版块: {positive_net_inflow} ({positive_net_inflow / total_sectors * 100:.1f}%)\n")
            f.write(f"主力净流出版块: {negative_net_inflow} ({negative_net_inflow / total_sectors * 100:.1f}%)\n")
            f.write(f"全市场主力净流入总额: {total_net_inflow:,.0f} 元\n")
            f.write(f"平均主力流入比例: {avg_inflow_ratio:.2f}%\n")
            f.write(f"平均主力流出比例: {avg_outflow_ratio:.2f}%\n\n")

            # 添加前10名净流入和前10名流入比例
            f.write("主力净流入前10名:\n")
            f.write("-" * 30 + "\n")
            net_top10 = df.nlargest(10, '主力净流入')
            for idx, (_, row) in enumerate(net_top10.iterrows(), 1):
                f.write(f"{idx:2d}. {row['版块名称']:<15} {row['主力净流入']:>12,.0f} 元\n")

            f.write("\n主力流入比例前10名:\n")
            f.write("-" * 30 + "\n")
            ratio_top10 = df.nlargest(10, '主力流入比例')
            for idx, (_, row) in enumerate(ratio_top10.iterrows(), 1):
                f.write(f"{idx:2d}. {row['版块名称']:<15} {row['主力流入比例']:>6.2f}%\n")

    except Exception as e:
        print(f"❌ 保存统计摘要失败: {e}")


def print_single_sector_data(data):
    """
    格式化打印单个版块的资金流数据（保留原有功能）
    """
    if not data:
        print("❌ 无有效数据可显示")
        return

    print("\n" + "=" * 50)
    print("📊 TPDog 行业资金流数据")
    print("=" * 50)

    print(f"板块代码: {data.get('code', 'N/A')}")
    print(f"板块名称: {data.get('name', 'N/A')}")
    print(f"统计期间: {data.get('start', 'N/A')} ~ {data.get('end', 'N/A')}")

    print("\n💰 主力资金:")
    print(f"  流入: {data.get('m_in', 0):,.0f} 元")
    print(f"  流出: {data.get('m_out', 0):,.0f} 元")
    print(f"  净流入: {data.get('m_net', 0):,.0f} 元")
    print(f"  流入比例: {data.get('m_in_ratio', 0):.2f}%")
    print(f"  流出比例: {data.get('m_out_ratio', 0):.2f}%")

    print("\n👥 散户资金:")
    print(f"  流入: {data.get('r_in', 0):,.0f} 元")
    print(f"  流出: {data.get('r_out', 0):,.0f} 元")
    print(f"  净流入: {data.get('r_net', 0):,.0f} 元")
    print(f"  流入比例: {data.get('r_in_ratio', 0):.2f}%")
    print(f"  流出比例: {data.get('r_out_ratio', 0):.2f}%")

    # 资金流向分析
    m_net = data.get('m_net', 0)
    r_net = data.get('r_net', 0)

    print("\n📈 资金流向分析:")
    if m_net > 0:
        print(f"  ✅ 主力净流入 {m_net:,.0f} 元")
    else:
        print(f"  ❌ 主力净流出 {abs(m_net):,.0f} 元")

    if r_net > 0:
        print(f"  ✅ 散户净流入 {r_net:,.0f} 元")
    else:
        print(f"  ❌ 散户净流出 {abs(r_net):,.0f} 元")

    print("=" * 50)


def print_cache_info():
    """
    显示缓存信息
    """
    cache_data = load_sector_cache()

    if not cache_data:
        print("📝 暂无缓存数据")
        return

    print("\n" + "=" * 60)
    print("🗄️ 版块数据缓存信息")
    print("=" * 60)

    today = datetime.now().strftime('%Y-%m-%d')

    sector_names = {
        'bki': '行业版块',
        'bkc': '概念版块',
        'bkr': '地域版块'
    }

    for sector_type, type_name in sector_names.items():
        if sector_type in cache_data:
            cache_info = cache_data[sector_type]
            cache_date = cache_info.get('cache_date', 'N/A')
            cache_time = cache_info.get('cache_time', 'N/A')
            total_count = cache_info.get('total_count', 0)

            status = "✅ 今日缓存" if cache_date == today else "⚠️ 过期缓存"

            print(f"{status} {type_name}({sector_type}): {total_count}个版块")
            print(f"       缓存时间: {cache_time}")
        else:
            print(f"❌ 无缓存 {type_name}({sector_type})")

    print("-" * 60)
    print(f"📅 当前日期: {today}")
    print(f"🔧 强制更新: {'开启' if FORCE_UPDATE else '关闭'}")


def clear_cache():
    """
    清理缓存数据
    """
    try:
        cache_file = os.path.join(get_date_folder(), CACHE_FILE_NAME)
        if os.path.exists(cache_file):
            os.remove(cache_file)
            print(f"🗑️ 缓存文件已清理: {cache_file}")
        else:
            print("📝 无缓存文件需要清理")
    except Exception as e:
        print(f"❌ 清理缓存失败: {e}")


def print_fetch_records():
    """
    打印获取记录统计
    """
    records = load_fetch_records()

    if not records:
        print("📝 暂无获取记录")
        return

    print("\n" + "=" * 60)
    print("📝 今日获取记录统计")
    print("=" * 60)

    success_count = 0
    failed_count = 0
    skipped_count = 0

    for _, record in records.items():
        status = record.get('status', 'unknown')
        if status == 'success':
            success_count += 1
        elif status == 'failed':
            failed_count += 1
        elif status == 'skipped':
            skipped_count += 1

    print(f"✅ 成功获取: {success_count} 个版块")
    print(f"❌ 获取失败: {failed_count} 个版块")
    print(f"⏭️ 跳过版块: {skipped_count} 个版块")
    print(f"📊 总计记录: {len(records)} 个版块")

    # 显示最近的几条记录
    print(f"\n最近5条记录:")
    print("-" * 60)

    sorted_records = sorted(records.items(),
                            key=lambda x: x[1].get('last_fetch_time', ''),
                            reverse=True)

    for _, record in sorted_records[:5]:
        status_icon = {'success': '✅', 'failed': '❌', 'skipped': '⏭️'}.get(record.get('status'), '❓')
        print(
            f"{status_icon} {record.get('sector_name', 'N/A'):<20} {record.get('last_fetch_time', 'N/A'):<20} {record.get('message', 'N/A')}")


def main():
    """
    主函数
    """
    global FORCE_UPDATE

    print("🚀 TPDog 行业资金流数据获取工具启动...")
    print(f"🔧 强制更新模式: {'开启' if FORCE_UPDATE else '关闭'}")

    # 1. 加载Token
    token = load_tpdog_token()
    if not token:
        return

    # 2. 选择运行模式
    print("\n请选择运行模式:")
    print("1. 获取单个版块资金流数据（原功能）")
    print("2. 获取所有行业版块资金流排行榜")
    print("3. 获取所有概念版块资金流排行榜")
    print("4. 获取所有地域版块资金流排行榜")
    print("5. 查看今日获取记录")
    print("6. 切换强制更新模式")
    print("7. 查看缓存信息")
    print("8. 清理缓存数据")

    try:
        choice = input("请输入选择 (1-8): ").strip()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
        return

    if choice == "1":
        # 原有的单个版块查询功能
        print(f"\n📊 正在获取单个版块资金流数据...")
        data = get_industry_fund_flow(token, "bki.880115")  # 默认专业服务板块
        if data:
            print_single_sector_data(data)

    elif choice in ["2", "3", "4"]:
        # 批量获取所有版块数据
        sector_types = {
            "2": ("bki", "行业版块"),
            "3": ("bkc", "概念版块"),
            "4": ("bkr", "地域版块")
        }

        sector_type, type_name = sector_types[choice]

        print(f"\n📋 正在获取{type_name}列表...")
        sectors = get_sector_list(token, sector_type)

        if not sectors:
            print("❌ 获取版块列表失败")
            return

        # 获取所有版块的资金流数据
        fund_flow_data = get_all_sectors_fund_flow(token, sectors)

        if fund_flow_data:
            # 创建并显示排行榜
            df = create_rankings(fund_flow_data)

            # 保存数据到文件（传入版块列表用于原始数据保存）
            if df is not None:
                save_data_to_files(df, fund_flow_data, type_name, sectors)
        else:
            print("❌ 未获取到有效的资金流数据")

    elif choice == "5":
        # 查看今日获取记录
        print_fetch_records()

    elif choice == "6":
        # 切换强制更新模式
        current_mode = FORCE_UPDATE
        FORCE_UPDATE = not current_mode
        print(f"🔧 强制更新模式已{'开启' if FORCE_UPDATE else '关闭'}")
        print("⚠️ 注意: 此设置仅在当前运行中生效，重启程序后恢复默认设置")
        # 返回主菜单
        main()
        return

    elif choice == "7":
        # 查看缓存信息
        print_cache_info()

    elif choice == "8":
        # 清理缓存数据
        try:
            confirm = input("确认清理所有缓存数据？(y/N): ").strip().lower()
            if confirm == 'y':
                clear_cache()
            else:
                print("⏭️ 已取消清理操作")
        except KeyboardInterrupt:
            print("\n⏭️ 已取消清理操作")

    else:
        print("❌ 无效选择，程序退出")
        return

    print(f"\n{'=' * 50}")
    print("💡 提示:")
    print("   1. 数据来源于TPDog API，更新频率为30分钟")
    print("   2. 请确保在 .env 文件中正确设置了 TPDOG_TOKEN")
    print("   3. API限制为30次/秒，程序已自动控制请求频率")
    print("   4. 所有数据已按日期保存到 fund_flow 文件夹中")
    print("   5. 保存的文件包括:")
    print("      - 完整排行榜数据 (CSV)")
    print("      - 净流入TOP100 (CSV)")
    print("      - 流入比例TOP100 (CSV)")
    print("      - 原始数据 (JSON)")
    print("      - 统计摘要 (TXT)")
    print("      - 获取记录 (JSON)")
    print("      - 版块缓存 (JSON)")
    print(f"   6. 强制更新模式: {'开启' if FORCE_UPDATE else '关闭'}")
    print("   7. 收盘后(15:00后)会自动跳过已获取的版块数据")
    print("   8. 每次运行都会记录获取状态和时间")
    print("   9. 版块列表支持缓存，同一天只获取一次")
    print("  10. 原始数据保存在 raw_data 子目录中")


if __name__ == "__main__":
    main()