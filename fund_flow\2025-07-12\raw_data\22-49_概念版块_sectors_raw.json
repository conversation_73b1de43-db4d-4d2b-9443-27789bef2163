{"timestamp": "2025-07-12 22:49:27", "sector_type": "概念版块", "total_count": 473, "sectors": [{"code": "880363", "name": "财税数字化", "type": "bkc", "req_code": "bkc.880363"}, {"code": "880368", "name": "玻璃基板", "type": "bkc", "req_code": "bkc.880368"}, {"code": "880371", "name": "合成生物", "type": "bkc", "req_code": "bkc.880371"}, {"code": "880372", "name": "锂矿概念", "type": "bkc", "req_code": "bkc.880372"}, {"code": "880373", "name": "AI语料", "type": "bkc", "req_code": "bkc.880373"}, {"code": "880374", "name": "碳纤维", "type": "bkc", "req_code": "bkc.880374"}, {"code": "880377", "name": "AI制药（医疗）", "type": "bkc", "req_code": "bkc.880377"}, {"code": "880379", "name": "<PERSON><PERSON>概念", "type": "bkc", "req_code": "bkc.880379"}, {"code": "880384", "name": "铜缆高速连接", "type": "bkc", "req_code": "bkc.880384"}, {"code": "880389", "name": "生物医药", "type": "bkc", "req_code": "bkc.880389"}, {"code": "880394", "name": "低空经济", "type": "bkc", "req_code": "bkc.880394"}, {"code": "880398", "name": "娃哈哈概念", "type": "bkc", "req_code": "bkc.880398"}, {"code": "880399", "name": "AIPC", "type": "bkc", "req_code": "bkc.880399"}, {"code": "880404", "name": "可控核聚变", "type": "bkc", "req_code": "bkc.880404"}, {"code": "880407", "name": "AI手机", "type": "bkc", "req_code": "bkc.880407"}, {"code": "880408", "name": "英伟达概念", "type": "bkc", "req_code": "bkc.880408"}, {"code": "880409", "name": "柔性屏(折叠屏)", "type": "bkc", "req_code": "bkc.880409"}, {"code": "880412", "name": "Sora概念", "type": "bkc", "req_code": "bkc.880412"}, {"code": "880416", "name": "微盘股", "type": "bkc", "req_code": "bkc.880416"}, {"code": "880419", "name": "飞行汽车(eVTOL)", "type": "bkc", "req_code": "bkc.880419"}, {"code": "880422", "name": "PEEK材料概念", "type": "bkc", "req_code": "bkc.880422"}, {"code": "880425", "name": "小米汽车", "type": "bkc", "req_code": "bkc.880425"}, {"code": "880426", "name": "东盟自贸区概念", "type": "bkc", "req_code": "bkc.880426"}, {"code": "880428", "name": "多模态AI", "type": "bkc", "req_code": "bkc.880428"}, {"code": "880430", "name": "高带宽内存", "type": "bkc", "req_code": "bkc.880430"}, {"code": "880431", "name": "短剧互动游戏", "type": "bkc", "req_code": "bkc.880431"}, {"code": "880435", "name": "新型工业化", "type": "bkc", "req_code": "bkc.880435"}, {"code": "880438", "name": "星闪概念", "type": "bkc", "req_code": "bkc.880438"}, {"code": "880441", "name": "BC电池", "type": "bkc", "req_code": "bkc.880441"}, {"code": "880445", "name": "SPD概念", "type": "bkc", "req_code": "bkc.880445"}, {"code": "880446", "name": "减肥药", "type": "bkc", "req_code": "bkc.880446"}, {"code": "880448", "name": "机器人执行器", "type": "bkc", "req_code": "bkc.880448"}, {"code": "880450", "name": "高压快充", "type": "bkc", "req_code": "bkc.880450"}, {"code": "880451", "name": "空间计算", "type": "bkc", "req_code": "bkc.880451"}, {"code": "880453", "name": "裸眼3D", "type": "bkc", "req_code": "bkc.880453"}, {"code": "880458", "name": "混合现实", "type": "bkc", "req_code": "bkc.880458"}, {"code": "880460", "name": "央企改革", "type": "bkc", "req_code": "bkc.880460"}, {"code": "880465", "name": "中特估", "type": "bkc", "req_code": "bkc.880465"}, {"code": "880468", "name": "液冷概念", "type": "bkc", "req_code": "bkc.880468"}, {"code": "880469", "name": "存储芯片", "type": "bkc", "req_code": "bkc.880469"}, {"code": "880471", "name": "光通信模块", "type": "bkc", "req_code": "bkc.880471"}, {"code": "880476", "name": "数据要素", "type": "bkc", "req_code": "bkc.880476"}, {"code": "880479", "name": "算力概念", "type": "bkc", "req_code": "bkc.880479"}, {"code": "880483", "name": "ERP概念", "type": "bkc", "req_code": "bkc.880483"}, {"code": "880484", "name": "MLOps概念", "type": "bkc", "req_code": "bkc.880484"}, {"code": "880487", "name": "同步磁阻电机", "type": "bkc", "req_code": "bkc.880487"}, {"code": "880488", "name": "时空大数据", "type": "bkc", "req_code": "bkc.880488"}, {"code": "880489", "name": "数字水印", "type": "bkc", "req_code": "bkc.880489"}, {"code": "880493", "name": "CPO概念", "type": "bkc", "req_code": "bkc.880493"}, {"code": "880497", "name": "AI芯片", "type": "bkc", "req_code": "bkc.880497"}, {"code": "880502", "name": "ChatGPT概念", "type": "bkc", "req_code": "bkc.880502"}, {"code": "880503", "name": "电子后视镜", "type": "bkc", "req_code": "bkc.880503"}, {"code": "880506", "name": "毫米波概念", "type": "bkc", "req_code": "bkc.880506"}, {"code": "880510", "name": "蒙脱石散", "type": "bkc", "req_code": "bkc.880510"}, {"code": "880515", "name": "血氧仪", "type": "bkc", "req_code": "bkc.880515"}, {"code": "880519", "name": "第四代半导体", "type": "bkc", "req_code": "bkc.880519"}, {"code": "880523", "name": "熊去氧胆酸", "type": "bkc", "req_code": "bkc.880523"}, {"code": "880525", "name": "PLC概念", "type": "bkc", "req_code": "bkc.880525"}, {"code": "880528", "name": "数据确权", "type": "bkc", "req_code": "bkc.880528"}, {"code": "880529", "name": "抗原检测", "type": "bkc", "req_code": "bkc.880529"}, {"code": "880530", "name": "抗菌面料", "type": "bkc", "req_code": "bkc.880530"}, {"code": "880531", "name": "跨境电商", "type": "bkc", "req_code": "bkc.880531"}, {"code": "880532", "name": "人造太阳", "type": "bkc", "req_code": "bkc.880532"}, {"code": "880535", "name": "复合集流体", "type": "bkc", "req_code": "bkc.880535"}, {"code": "880537", "name": "破净股", "type": "bkc", "req_code": "bkc.880537"}, {"code": "880540", "name": "AIGC概念", "type": "bkc", "req_code": "bkc.880540"}, {"code": "880544", "name": "Web3.0", "type": "bkc", "req_code": "bkc.880544"}, {"code": "880546", "name": "供销社概念", "type": "bkc", "req_code": "bkc.880546"}, {"code": "880550", "name": "科创板做市股", "type": "bkc", "req_code": "bkc.880550"}, {"code": "880555", "name": "科创板做市商", "type": "bkc", "req_code": "bkc.880555"}, {"code": "880558", "name": "创新药", "type": "bkc", "req_code": "bkc.880558"}, {"code": "880560", "name": "世界杯", "type": "bkc", "req_code": "bkc.880560"}, {"code": "880565", "name": "信创", "type": "bkc", "req_code": "bkc.880565"}, {"code": "880566", "name": "熔盐储能", "type": "bkc", "req_code": "bkc.880566"}, {"code": "880567", "name": "空气能热泵", "type": "bkc", "req_code": "bkc.880567"}, {"code": "880572", "name": "Chiplet概念", "type": "bkc", "req_code": "bkc.880572"}, {"code": "880575", "name": "减速器", "type": "bkc", "req_code": "bkc.880575"}, {"code": "880577", "name": "轮毂电机", "type": "bkc", "req_code": "bkc.880577"}, {"code": "880581", "name": "生物质能发电", "type": "bkc", "req_code": "bkc.880581"}, {"code": "880586", "name": "TOPCon电池", "type": "bkc", "req_code": "bkc.880586"}, {"code": "880590", "name": "光伏高速公路", "type": "bkc", "req_code": "bkc.880590"}, {"code": "880593", "name": "钒电池", "type": "bkc", "req_code": "bkc.880593"}, {"code": "880597", "name": "钙钛矿电池", "type": "bkc", "req_code": "bkc.880597"}, {"code": "880598", "name": "汽车一体化压铸", "type": "bkc", "req_code": "bkc.880598"}, {"code": "880600", "name": "麒麟电池", "type": "bkc", "req_code": "bkc.880600"}, {"code": "880603", "name": "机器人概念", "type": "bkc", "req_code": "bkc.880603"}, {"code": "880606", "name": "汽车热管理", "type": "bkc", "req_code": "bkc.880606"}, {"code": "880609", "name": "F5G概念", "type": "bkc", "req_code": "bkc.880609"}, {"code": "880611", "name": "超超临界发电", "type": "bkc", "req_code": "bkc.880611"}, {"code": "880616", "name": "粮食概念", "type": "bkc", "req_code": "bkc.880616"}, {"code": "880617", "name": "痘病毒防治", "type": "bkc", "req_code": "bkc.880617"}, {"code": "880620", "name": "数字哨兵", "type": "bkc", "req_code": "bkc.880620"}, {"code": "880622", "name": "千金藤素", "type": "bkc", "req_code": "bkc.880622"}, {"code": "880624", "name": "噪声防治", "type": "bkc", "req_code": "bkc.880624"}, {"code": "880625", "name": "新型城镇化", "type": "bkc", "req_code": "bkc.880625"}, {"code": "880629", "name": "户外露营", "type": "bkc", "req_code": "bkc.880629"}, {"code": "880633", "name": "肝炎概念", "type": "bkc", "req_code": "bkc.880633"}, {"code": "880634", "name": "统一大市场", "type": "bkc", "req_code": "bkc.880634"}, {"code": "880638", "name": "建筑节能", "type": "bkc", "req_code": "bkc.880638"}, {"code": "880639", "name": "电子身份证", "type": "bkc", "req_code": "bkc.880639"}, {"code": "880641", "name": "托育服务", "type": "bkc", "req_code": "bkc.880641"}, {"code": "880644", "name": "啤酒概念", "type": "bkc", "req_code": "bkc.880644"}, {"code": "880649", "name": "中俄贸易概念", "type": "bkc", "req_code": "bkc.880649"}, {"code": "880653", "name": "跨境支付", "type": "bkc", "req_code": "bkc.880653"}, {"code": "880658", "name": "土壤修复", "type": "bkc", "req_code": "bkc.880658"}, {"code": "880663", "name": "智慧灯杆", "type": "bkc", "req_code": "bkc.880663"}, {"code": "880664", "name": "净水概念", "type": "bkc", "req_code": "bkc.880664"}, {"code": "880668", "name": "杭州亚运会", "type": "bkc", "req_code": "bkc.880668"}, {"code": "880672", "name": "民爆概念", "type": "bkc", "req_code": "bkc.880672"}, {"code": "880676", "name": "气溶胶检测", "type": "bkc", "req_code": "bkc.880676"}, {"code": "880679", "name": "东数西算", "type": "bkc", "req_code": "bkc.880679"}, {"code": "880682", "name": "重组蛋白", "type": "bkc", "req_code": "bkc.880682"}, {"code": "880687", "name": "新冠检测", "type": "bkc", "req_code": "bkc.880687"}, {"code": "880688", "name": "数字经济", "type": "bkc", "req_code": "bkc.880688"}, {"code": "880692", "name": "新冠药物", "type": "bkc", "req_code": "bkc.880692"}, {"code": "880694", "name": "百元股", "type": "bkc", "req_code": "bkc.880694"}, {"code": "880696", "name": "地下管网", "type": "bkc", "req_code": "bkc.880696"}, {"code": "880701", "name": "电子纸概念", "type": "bkc", "req_code": "bkc.880701"}, {"code": "880704", "name": "幽门螺杆菌概念", "type": "bkc", "req_code": "bkc.880704"}, {"code": "880709", "name": "虚拟数字人", "type": "bkc", "req_code": "bkc.880709"}, {"code": "880713", "name": "DRG/DIP", "type": "bkc", "req_code": "bkc.880713"}, {"code": "880714", "name": "低价股", "type": "bkc", "req_code": "bkc.880714"}, {"code": "880719", "name": "动力电池回收", "type": "bkc", "req_code": "bkc.880719"}, {"code": "880721", "name": "昨日连板_含一字", "type": "bkc", "req_code": "bkc.880721"}, {"code": "880726", "name": "昨日涨停_含一字", "type": "bkc", "req_code": "bkc.880726"}, {"code": "880731", "name": "EDR概念", "type": "bkc", "req_code": "bkc.880731"}, {"code": "880732", "name": "IGBT概念", "type": "bkc", "req_code": "bkc.880732"}, {"code": "880733", "name": "数据安全", "type": "bkc", "req_code": "bkc.880733"}, {"code": "880738", "name": "调味品概念", "type": "bkc", "req_code": "bkc.880738"}, {"code": "880743", "name": "预制菜概念", "type": "bkc", "req_code": "bkc.880743"}, {"code": "880747", "name": "绿色电力", "type": "bkc", "req_code": "bkc.880747"}, {"code": "880750", "name": "培育钻石", "type": "bkc", "req_code": "bkc.880750"}, {"code": "880751", "name": "职业教育", "type": "bkc", "req_code": "bkc.880751"}, {"code": "880753", "name": "发电机概念", "type": "bkc", "req_code": "bkc.880753"}, {"code": "880757", "name": "华为欧拉", "type": "bkc", "req_code": "bkc.880757"}, {"code": "880759", "name": "PVDF概念", "type": "bkc", "req_code": "bkc.880759"}, {"code": "880764", "name": "环氧丙烷", "type": "bkc", "req_code": "bkc.880764"}, {"code": "880767", "name": "磷化工", "type": "bkc", "req_code": "bkc.880767"}, {"code": "880771", "name": "元宇宙概念", "type": "bkc", "req_code": "bkc.880771"}, {"code": "880776", "name": "国资云概念", "type": "bkc", "req_code": "bkc.880776"}, {"code": "880778", "name": "植物照明", "type": "bkc", "req_code": "bkc.880778"}, {"code": "880779", "name": "碳基材料", "type": "bkc", "req_code": "bkc.880779"}, {"code": "880780", "name": "专精特新", "type": "bkc", "req_code": "bkc.880780"}, {"code": "880785", "name": "工业母机", "type": "bkc", "req_code": "bkc.880785"}, {"code": "880787", "name": "抽水蓄能", "type": "bkc", "req_code": "bkc.880787"}, {"code": "880788", "name": "激光雷达", "type": "bkc", "req_code": "bkc.880788"}, {"code": "880792", "name": "内贸流通", "type": "bkc", "req_code": "bkc.880792"}, {"code": "880793", "name": "宁组合", "type": "bkc", "req_code": "bkc.880793"}, {"code": "880796", "name": "茅指数", "type": "bkc", "req_code": "bkc.880796"}, {"code": "880800", "name": "机器视觉", "type": "bkc", "req_code": "bkc.880800"}, {"code": "880803", "name": "NFT概念", "type": "bkc", "req_code": "bkc.880803"}, {"code": "880804", "name": "毛发医疗", "type": "bkc", "req_code": "bkc.880804"}, {"code": "880805", "name": "华为昇腾", "type": "bkc", "req_code": "bkc.880805"}, {"code": "880809", "name": "空间站概念", "type": "bkc", "req_code": "bkc.880809"}, {"code": "880814", "name": "宠物经济", "type": "bkc", "req_code": "bkc.880814"}, {"code": "880816", "name": "REITs概念", "type": "bkc", "req_code": "bkc.880816"}, {"code": "880821", "name": "工程机械概念", "type": "bkc", "req_code": "bkc.880821"}, {"code": "880825", "name": "快递概念", "type": "bkc", "req_code": "bkc.880825"}, {"code": "880829", "name": "储能", "type": "bkc", "req_code": "bkc.880829"}, {"code": "880832", "name": "钠离子电池", "type": "bkc", "req_code": "bkc.880832"}, {"code": "880836", "name": "CAR-T细胞疗法", "type": "bkc", "req_code": "bkc.880836"}, {"code": "880839", "name": "换电概念", "type": "bkc", "req_code": "bkc.880839"}, {"code": "880840", "name": "华为汽车", "type": "bkc", "req_code": "bkc.880840"}, {"code": "880843", "name": "核污染防治", "type": "bkc", "req_code": "bkc.880843"}, {"code": "880846", "name": "电子车牌", "type": "bkc", "req_code": "bkc.880846"}, {"code": "880850", "name": "工业气体", "type": "bkc", "req_code": "bkc.880850"}, {"code": "880855", "name": "化债(AMC)概念", "type": "bkc", "req_code": "bkc.880855"}, {"code": "880856", "name": "低碳冶金", "type": "bkc", "req_code": "bkc.880856"}, {"code": "880860", "name": "光伏建筑一体化", "type": "bkc", "req_code": "bkc.880860"}, {"code": "880865", "name": "碳化硅", "type": "bkc", "req_code": "bkc.880865"}, {"code": "880869", "name": "被动元件", "type": "bkc", "req_code": "bkc.880869"}, {"code": "880870", "name": "磁悬浮概念", "type": "bkc", "req_code": "bkc.880870"}, {"code": "880871", "name": "化妆品概念", "type": "bkc", "req_code": "bkc.880871"}, {"code": "880874", "name": "注射器概念", "type": "bkc", "req_code": "bkc.880874"}, {"code": "880876", "name": "快手概念", "type": "bkc", "req_code": "bkc.880876"}, {"code": "880879", "name": "注册制次新股", "type": "bkc", "req_code": "bkc.880879"}, {"code": "880881", "name": "生物识别", "type": "bkc", "req_code": "bkc.880881"}, {"code": "880883", "name": "汽车芯片", "type": "bkc", "req_code": "bkc.880883"}, {"code": "880886", "name": "固态电池", "type": "bkc", "req_code": "bkc.880886"}, {"code": "880891", "name": "水产养殖", "type": "bkc", "req_code": "bkc.880891"}, {"code": "880896", "name": "碳交易", "type": "bkc", "req_code": "bkc.880896"}, {"code": "880900", "name": "社区团购", "type": "bkc", "req_code": "bkc.880900"}, {"code": "880902", "name": "6G概念", "type": "bkc", "req_code": "bkc.880902"}, {"code": "880904", "name": "商业航天", "type": "bkc", "req_code": "bkc.880904"}, {"code": "880906", "name": "RCEP概念", "type": "bkc", "req_code": "bkc.880906"}, {"code": "880908", "name": "有机硅", "type": "bkc", "req_code": "bkc.880908"}, {"code": "880910", "name": "无线充电", "type": "bkc", "req_code": "bkc.880910"}, {"code": "880913", "name": "数字阅读", "type": "bkc", "req_code": "bkc.880913"}, {"code": "880918", "name": "虚拟电厂", "type": "bkc", "req_code": "bkc.880918"}, {"code": "880923", "name": "拼多多概念", "type": "bkc", "req_code": "bkc.880923"}, {"code": "880927", "name": "eSIM", "type": "bkc", "req_code": "bkc.880927"}, {"code": "880929", "name": "C2M概念", "type": "bkc", "req_code": "bkc.880929"}, {"code": "880931", "name": "盲盒经济", "type": "bkc", "req_code": "bkc.880931"}, {"code": "880933", "name": "鸿蒙概念", "type": "bkc", "req_code": "bkc.880933"}, {"code": "880938", "name": "第三代半导体", "type": "bkc", "req_code": "bkc.880938"}, {"code": "880939", "name": "刀片电池", "type": "bkc", "req_code": "bkc.880939"}, {"code": "880941", "name": "草甘膦", "type": "bkc", "req_code": "bkc.880941"}, {"code": "880945", "name": "氦气概念", "type": "bkc", "req_code": "bkc.880945"}, {"code": "880949", "name": "MicroLED", "type": "bkc", "req_code": "bkc.880949"}, {"code": "880954", "name": "屏下摄像", "type": "bkc", "req_code": "bkc.880954"}, {"code": "880958", "name": "EDA概念", "type": "bkc", "req_code": "bkc.880958"}, {"code": "880962", "name": "装配建筑", "type": "bkc", "req_code": "bkc.880962"}, {"code": "880964", "name": "肝素概念", "type": "bkc", "req_code": "bkc.880964"}, {"code": "880966", "name": "汽车拆解", "type": "bkc", "req_code": "bkc.880966"}, {"code": "880969", "name": "商汤概念", "type": "bkc", "req_code": "bkc.880969"}, {"code": "880971", "name": "疫苗冷链", "type": "bkc", "req_code": "bkc.880971"}, {"code": "880973", "name": "网红经济", "type": "bkc", "req_code": "bkc.880973"}, {"code": "880974", "name": "辅助生殖", "type": "bkc", "req_code": "bkc.880974"}, {"code": "880975", "name": "代糖概念", "type": "bkc", "req_code": "bkc.880975"}, {"code": "880979", "name": "蚂蚁概念", "type": "bkc", "req_code": "bkc.880979"}, {"code": "880980", "name": "长寿药", "type": "bkc", "req_code": "bkc.880980"}, {"code": "880981", "name": "中芯概念", "type": "bkc", "req_code": "bkc.880981"}, {"code": "880985", "name": "蝗虫防治", "type": "bkc", "req_code": "bkc.880985"}, {"code": "880987", "name": "退税商店", "type": "bkc", "req_code": "bkc.880987"}, {"code": "880988", "name": "尾气治理", "type": "bkc", "req_code": "bkc.880988"}, {"code": "880992", "name": "地塞米松", "type": "bkc", "req_code": "bkc.880992"}, {"code": "880994", "name": "抖音小店", "type": "bkc", "req_code": "bkc.880994"}, {"code": "880998", "name": "免税概念", "type": "bkc", "req_code": "bkc.880998"}, {"code": "881003", "name": "湖北自贸", "type": "bkc", "req_code": "bkc.881003"}, {"code": "881005", "name": "北交所概念", "type": "bkc", "req_code": "bkc.881005"}, {"code": "881007", "name": "地摊经济", "type": "bkc", "req_code": "bkc.881007"}, {"code": "881008", "name": "抖音概念(字节概念)", "type": "bkc", "req_code": "bkc.881008"}, {"code": "881009", "name": "数据中心", "type": "bkc", "req_code": "bkc.881009"}, {"code": "881010", "name": "卫星互联网", "type": "bkc", "req_code": "bkc.881010"}, {"code": "881015", "name": "车联网(车路云)", "type": "bkc", "req_code": "bkc.881015"}, {"code": "881017", "name": "RCS概念", "type": "bkc", "req_code": "bkc.881017"}, {"code": "881021", "name": "特高压", "type": "bkc", "req_code": "bkc.881021"}, {"code": "881022", "name": "半导体概念", "type": "bkc", "req_code": "bkc.881022"}, {"code": "881023", "name": "氮化镓", "type": "bkc", "req_code": "bkc.881023"}, {"code": "881025", "name": "WiFi", "type": "bkc", "req_code": "bkc.881025"}, {"code": "881029", "name": "医废处理", "type": "bkc", "req_code": "bkc.881029"}, {"code": "881030", "name": "消毒剂", "type": "bkc", "req_code": "bkc.881030"}, {"code": "881033", "name": "远程办公", "type": "bkc", "req_code": "bkc.881033"}, {"code": "881034", "name": "口罩", "type": "bkc", "req_code": "bkc.881034"}, {"code": "881035", "name": "降解塑料", "type": "bkc", "req_code": "bkc.881035"}, {"code": "881038", "name": "HIT电池", "type": "bkc", "req_code": "bkc.881038"}, {"code": "881043", "name": "转基因", "type": "bkc", "req_code": "bkc.881043"}, {"code": "881044", "name": "流感", "type": "bkc", "req_code": "bkc.881044"}, {"code": "881048", "name": "传感器", "type": "bkc", "req_code": "bkc.881048"}, {"code": "881050", "name": "广电", "type": "bkc", "req_code": "bkc.881050"}, {"code": "881053", "name": "云游戏", "type": "bkc", "req_code": "bkc.881053"}, {"code": "881057", "name": "MiniLED", "type": "bkc", "req_code": "bkc.881057"}, {"code": "881062", "name": "3D摄像头", "type": "bkc", "req_code": "bkc.881062"}, {"code": "881065", "name": "新能源车", "type": "bkc", "req_code": "bkc.881065"}, {"code": "881068", "name": "CRO", "type": "bkc", "req_code": "bkc.881068"}, {"code": "881069", "name": "胎压监测", "type": "bkc", "req_code": "bkc.881069"}, {"code": "881070", "name": "IPv6", "type": "bkc", "req_code": "bkc.881070"}, {"code": "881071", "name": "白酒", "type": "bkc", "req_code": "bkc.881071"}, {"code": "881072", "name": "维生素", "type": "bkc", "req_code": "bkc.881072"}, {"code": "881073", "name": "阿兹海默", "type": "bkc", "req_code": "bkc.881073"}, {"code": "881075", "name": "无线耳机", "type": "bkc", "req_code": "bkc.881075"}, {"code": "881076", "name": "乳业", "type": "bkc", "req_code": "bkc.881076"}, {"code": "881079", "name": "国产芯片", "type": "bkc", "req_code": "bkc.881079"}, {"code": "881084", "name": "MLCC", "type": "bkc", "req_code": "bkc.881084"}, {"code": "881088", "name": "医疗美容", "type": "bkc", "req_code": "bkc.881088"}, {"code": "881090", "name": "农业种植", "type": "bkc", "req_code": "bkc.881090"}, {"code": "881095", "name": "鸡肉概念", "type": "bkc", "req_code": "bkc.881095"}, {"code": "881096", "name": "智慧政务", "type": "bkc", "req_code": "bkc.881096"}, {"code": "881099", "name": "VPN", "type": "bkc", "req_code": "bkc.881099"}, {"code": "881100", "name": "光刻机(胶)", "type": "bkc", "req_code": "bkc.881100"}, {"code": "881102", "name": "数字货币", "type": "bkc", "req_code": "bkc.881102"}, {"code": "881105", "name": "猪肉概念", "type": "bkc", "req_code": "bkc.881105"}, {"code": "881109", "name": "3D玻璃", "type": "bkc", "req_code": "bkc.881109"}, {"code": "881111", "name": "UWB概念", "type": "bkc", "req_code": "bkc.881111"}, {"code": "881115", "name": "标准普尔", "type": "bkc", "req_code": "bkc.881115"}, {"code": "881120", "name": "分拆预期", "type": "bkc", "req_code": "bkc.881120"}, {"code": "881125", "name": "PCB", "type": "bkc", "req_code": "bkc.881125"}, {"code": "881127", "name": "ETC", "type": "bkc", "req_code": "bkc.881127"}, {"code": "881128", "name": "垃圾分类", "type": "bkc", "req_code": "bkc.881128"}, {"code": "881129", "name": "青蒿素", "type": "bkc", "req_code": "bkc.881129"}, {"code": "881133", "name": "单抗概念", "type": "bkc", "req_code": "bkc.881133"}, {"code": "881138", "name": "GDR", "type": "bkc", "req_code": "bkc.881138"}, {"code": "881141", "name": "富时罗素", "type": "bkc", "req_code": "bkc.881141"}, {"code": "881143", "name": "人造肉", "type": "bkc", "req_code": "bkc.881143"}, {"code": "881145", "name": "电子烟", "type": "bkc", "req_code": "bkc.881145"}, {"code": "881149", "name": "氢能源", "type": "bkc", "req_code": "bkc.881149"}, {"code": "881151", "name": "超级真菌", "type": "bkc", "req_code": "bkc.881151"}, {"code": "881154", "name": "数字孪生", "type": "bkc", "req_code": "bkc.881154"}, {"code": "881158", "name": "边缘计算", "type": "bkc", "req_code": "bkc.881158"}, {"code": "881163", "name": "超清视频", "type": "bkc", "req_code": "bkc.881163"}, {"code": "881167", "name": "工业大麻", "type": "bkc", "req_code": "bkc.881167"}, {"code": "881172", "name": "纳米银", "type": "bkc", "req_code": "bkc.881172"}, {"code": "881173", "name": "华为概念", "type": "bkc", "req_code": "bkc.881173"}, {"code": "881176", "name": "电子竞技", "type": "bkc", "req_code": "bkc.881176"}, {"code": "881178", "name": "冷链物流", "type": "bkc", "req_code": "bkc.881178"}, {"code": "881183", "name": "纾困概念", "type": "bkc", "req_code": "bkc.881183"}, {"code": "881187", "name": "进口博览", "type": "bkc", "req_code": "bkc.881187"}, {"code": "881192", "name": "京东金融", "type": "bkc", "req_code": "bkc.881192"}, {"code": "881197", "name": "影视概念", "type": "bkc", "req_code": "bkc.881197"}, {"code": "881202", "name": "百度概念", "type": "bkc", "req_code": "bkc.881202"}, {"code": "881203", "name": "天然气", "type": "bkc", "req_code": "bkc.881203"}, {"code": "881207", "name": "富士康", "type": "bkc", "req_code": "bkc.881207"}, {"code": "881209", "name": "体外诊断", "type": "bkc", "req_code": "bkc.881209"}, {"code": "881213", "name": "OLED", "type": "bkc", "req_code": "bkc.881213"}, {"code": "881214", "name": "知识产权", "type": "bkc", "req_code": "bkc.881214"}, {"code": "881219", "name": "东北振兴", "type": "bkc", "req_code": "bkc.881219"}, {"code": "881221", "name": "互联医疗", "type": "bkc", "req_code": "bkc.881221"}, {"code": "881222", "name": "独角兽", "type": "bkc", "req_code": "bkc.881222"}, {"code": "881227", "name": "乡村振兴", "type": "bkc", "req_code": "bkc.881227"}, {"code": "881228", "name": "小米概念", "type": "bkc", "req_code": "bkc.881228"}, {"code": "881229", "name": "工业互联", "type": "bkc", "req_code": "bkc.881229"}, {"code": "881232", "name": "万达概念", "type": "bkc", "req_code": "bkc.881232"}, {"code": "881237", "name": "区块链", "type": "bkc", "req_code": "bkc.881237"}, {"code": "881238", "name": "新零售", "type": "bkc", "req_code": "bkc.881238"}, {"code": "881239", "name": "养老金", "type": "bkc", "req_code": "bkc.881239"}, {"code": "881240", "name": "租售同权", "type": "bkc", "req_code": "bkc.881240"}, {"code": "881242", "name": "MSCI中国", "type": "bkc", "req_code": "bkc.881242"}, {"code": "881246", "name": "壳资源", "type": "bkc", "req_code": "bkc.881246"}, {"code": "881249", "name": "可燃冰", "type": "bkc", "req_code": "bkc.881249"}, {"code": "881251", "name": "昨日触板", "type": "bkc", "req_code": "bkc.881251"}, {"code": "881254", "name": "昨日连板", "type": "bkc", "req_code": "bkc.881254"}, {"code": "881257", "name": "昨日涨停", "type": "bkc", "req_code": "bkc.881257"}, {"code": "881262", "name": "大飞机", "type": "bkc", "req_code": "bkc.881262"}, {"code": "881265", "name": "雄安新区", "type": "bkc", "req_code": "bkc.881265"}, {"code": "881268", "name": "贬值受益", "type": "bkc", "req_code": "bkc.881268"}, {"code": "881270", "name": "超级品牌", "type": "bkc", "req_code": "bkc.881270"}, {"code": "881271", "name": "工业4.0", "type": "bkc", "req_code": "bkc.881271"}, {"code": "881273", "name": "军民融合", "type": "bkc", "req_code": "bkc.881273"}, {"code": "881276", "name": "共享经济", "type": "bkc", "req_code": "bkc.881276"}, {"code": "881277", "name": "精准医疗", "type": "bkc", "req_code": "bkc.881277"}, {"code": "881278", "name": "钛白粉", "type": "bkc", "req_code": "bkc.881278"}, {"code": "881282", "name": "深股通", "type": "bkc", "req_code": "bkc.881282"}, {"code": "881284", "name": "股权转让", "type": "bkc", "req_code": "bkc.881284"}, {"code": "881288", "name": "无人驾驶", "type": "bkc", "req_code": "bkc.881288"}, {"code": "881291", "name": "增强现实", "type": "bkc", "req_code": "bkc.881291"}, {"code": "881292", "name": "人工智能", "type": "bkc", "req_code": "bkc.881292"}, {"code": "881293", "name": "深证100R", "type": "bkc", "req_code": "bkc.881293"}, {"code": "881295", "name": "创业板综", "type": "bkc", "req_code": "bkc.881295"}, {"code": "881300", "name": "海绵城市", "type": "bkc", "req_code": "bkc.881300"}, {"code": "881305", "name": "高送转", "type": "bkc", "req_code": "bkc.881305"}, {"code": "881307", "name": "虚拟现实", "type": "bkc", "req_code": "bkc.881307"}, {"code": "881308", "name": "PPP模式", "type": "bkc", "req_code": "bkc.881308"}, {"code": "881310", "name": "健康中国", "type": "bkc", "req_code": "bkc.881310"}, {"code": "881312", "name": "证金持股", "type": "bkc", "req_code": "bkc.881312"}, {"code": "881317", "name": "北京冬奥", "type": "bkc", "req_code": "bkc.881317"}, {"code": "881318", "name": "航母概念", "type": "bkc", "req_code": "bkc.881318"}, {"code": "881321", "name": "5G概念", "type": "bkc", "req_code": "bkc.881321"}, {"code": "881324", "name": "2025规划", "type": "bkc", "req_code": "bkc.881324"}, {"code": "881327", "name": "一带一路", "type": "bkc", "req_code": "bkc.881327"}, {"code": "881328", "name": "券商概念", "type": "bkc", "req_code": "bkc.881328"}, {"code": "881332", "name": "量子科技", "type": "bkc", "req_code": "bkc.881332"}, {"code": "881337", "name": "赛马概念", "type": "bkc", "req_code": "bkc.881337"}, {"code": "881341", "name": "体育产业", "type": "bkc", "req_code": "bkc.881341"}, {"code": "881346", "name": "沪股通", "type": "bkc", "req_code": "bkc.881346"}, {"code": "881351", "name": "人脑工程", "type": "bkc", "req_code": "bkc.881351"}, {"code": "881353", "name": "上证380", "type": "bkc", "req_code": "bkc.881353"}, {"code": "881357", "name": "无人机", "type": "bkc", "req_code": "bkc.881357"}, {"code": "881361", "name": "超级电容", "type": "bkc", "req_code": "bkc.881361"}, {"code": "881362", "name": "中证500", "type": "bkc", "req_code": "bkc.881362"}, {"code": "881366", "name": "充电桩", "type": "bkc", "req_code": "bkc.881366"}, {"code": "881370", "name": "全息技术", "type": "bkc", "req_code": "bkc.881370"}, {"code": "881375", "name": "免疫治疗", "type": "bkc", "req_code": "bkc.881375"}, {"code": "881379", "name": "IPO受益", "type": "bkc", "req_code": "bkc.881379"}, {"code": "881382", "name": "国产软件", "type": "bkc", "req_code": "bkc.881382"}, {"code": "881384", "name": "小金属概念", "type": "bkc", "req_code": "bkc.881384"}, {"code": "881387", "name": "基因测序", "type": "bkc", "req_code": "bkc.881387"}, {"code": "881388", "name": "旅游概念", "type": "bkc", "req_code": "bkc.881388"}, {"code": "881391", "name": "氟化工", "type": "bkc", "req_code": "bkc.881391"}, {"code": "881393", "name": "阿里概念", "type": "bkc", "req_code": "bkc.881393"}, {"code": "881396", "name": "举牌", "type": "bkc", "req_code": "bkc.881396"}, {"code": "881400", "name": "京津冀", "type": "bkc", "req_code": "bkc.881400"}, {"code": "881404", "name": "央国企改革", "type": "bkc", "req_code": "bkc.881404"}, {"code": "881405", "name": "燃料电池", "type": "bkc", "req_code": "bkc.881405"}, {"code": "881409", "name": "智能家居", "type": "bkc", "req_code": "bkc.881409"}, {"code": "881410", "name": "超导概念", "type": "bkc", "req_code": "bkc.881410"}, {"code": "881411", "name": "粤港自贸", "type": "bkc", "req_code": "bkc.881411"}, {"code": "881412", "name": "独家药品", "type": "bkc", "req_code": "bkc.881412"}, {"code": "881416", "name": "病毒防治", "type": "bkc", "req_code": "bkc.881416"}, {"code": "881419", "name": "蓝宝石", "type": "bkc", "req_code": "bkc.881419"}, {"code": "881421", "name": "沪企改革", "type": "bkc", "req_code": "bkc.881421"}, {"code": "881423", "name": "彩票概念", "type": "bkc", "req_code": "bkc.881423"}, {"code": "881425", "name": "生态农业", "type": "bkc", "req_code": "bkc.881425"}, {"code": "881430", "name": "医疗器械概念", "type": "bkc", "req_code": "bkc.881430"}, {"code": "881433", "name": "国家安防", "type": "bkc", "req_code": "bkc.881433"}, {"code": "881436", "name": "苹果概念", "type": "bkc", "req_code": "bkc.881436"}, {"code": "881441", "name": "电商概念", "type": "bkc", "req_code": "bkc.881441"}, {"code": "881446", "name": "婴童概念", "type": "bkc", "req_code": "bkc.881446"}, {"code": "881449", "name": "在线教育", "type": "bkc", "req_code": "bkc.881449"}, {"code": "881454", "name": "智能电视", "type": "bkc", "req_code": "bkc.881454"}, {"code": "881455", "name": "网络安全", "type": "bkc", "req_code": "bkc.881455"}, {"code": "881457", "name": "养老概念", "type": "bkc", "req_code": "bkc.881457"}, {"code": "881460", "name": "特斯拉", "type": "bkc", "req_code": "bkc.881460"}, {"code": "881463", "name": "上海自贸", "type": "bkc", "req_code": "bkc.881463"}, {"code": "881465", "name": "手游概念", "type": "bkc", "req_code": "bkc.881465"}, {"code": "881467", "name": "智能穿戴", "type": "bkc", "req_code": "bkc.881467"}, {"code": "881472", "name": "智能机器", "type": "bkc", "req_code": "bkc.881472"}, {"code": "881473", "name": "创业成份", "type": "bkc", "req_code": "bkc.881473"}, {"code": "881476", "name": "互联金融", "type": "bkc", "req_code": "bkc.881476"}, {"code": "881479", "name": "B股", "type": "bkc", "req_code": "bkc.881479"}, {"code": "881482", "name": "中超概念", "type": "bkc", "req_code": "bkc.881482"}, {"code": "881483", "name": "大数据", "type": "bkc", "req_code": "bkc.881483"}, {"code": "881484", "name": "土地流转", "type": "bkc", "req_code": "bkc.881484"}, {"code": "881488", "name": "北斗导航", "type": "bkc", "req_code": "bkc.881488"}, {"code": "881490", "name": "智慧城市", "type": "bkc", "req_code": "bkc.881490"}, {"code": "881492", "name": "通用航空", "type": "bkc", "req_code": "bkc.881492"}, {"code": "881494", "name": "海洋经济", "type": "bkc", "req_code": "bkc.881494"}, {"code": "881497", "name": "地热能", "type": "bkc", "req_code": "bkc.881497"}, {"code": "881498", "name": "3D打印", "type": "bkc", "req_code": "bkc.881498"}, {"code": "881500", "name": "石墨烯", "type": "bkc", "req_code": "bkc.881500"}, {"code": "881503", "name": "中药概念", "type": "bkc", "req_code": "bkc.881503"}, {"code": "881505", "name": "食品安全", "type": "bkc", "req_code": "bkc.881505"}, {"code": "881506", "name": "上证180_", "type": "bkc", "req_code": "bkc.881506"}, {"code": "881507", "name": "上证50_", "type": "bkc", "req_code": "bkc.881507"}, {"code": "881510", "name": "央视50_", "type": "bkc", "req_code": "bkc.881510"}, {"code": "881512", "name": "油气设服", "type": "bkc", "req_code": "bkc.881512"}, {"code": "881513", "name": "参股保险", "type": "bkc", "req_code": "bkc.881513"}, {"code": "881515", "name": "页岩气", "type": "bkc", "req_code": "bkc.881515"}, {"code": "881517", "name": "海工装备", "type": "bkc", "req_code": "bkc.881517"}, {"code": "881519", "name": "参股新三板", "type": "bkc", "req_code": "bkc.881519"}, {"code": "881522", "name": "水利建设", "type": "bkc", "req_code": "bkc.881522"}, {"code": "881523", "name": "融资融券", "type": "bkc", "req_code": "bkc.881523"}, {"code": "881525", "name": "风能", "type": "bkc", "req_code": "bkc.881525"}, {"code": "881528", "name": "长江三角", "type": "bkc", "req_code": "bkc.881528"}, {"code": "881531", "name": "铁路基建", "type": "bkc", "req_code": "bkc.881531"}, {"code": "881534", "name": "太阳能", "type": "bkc", "req_code": "bkc.881534"}, {"code": "881539", "name": "智能电网", "type": "bkc", "req_code": "bkc.881539"}, {"code": "881540", "name": "LED", "type": "bkc", "req_code": "bkc.881540"}, {"code": "881542", "name": "云计算", "type": "bkc", "req_code": "bkc.881542"}, {"code": "881543", "name": "稀土永磁", "type": "bkc", "req_code": "bkc.881543"}, {"code": "881544", "name": "核能核电", "type": "bkc", "req_code": "bkc.881544"}, {"code": "881549", "name": "锂电池", "type": "bkc", "req_code": "bkc.881549"}, {"code": "881552", "name": "预盈预增", "type": "bkc", "req_code": "bkc.881552"}, {"code": "881553", "name": "预亏预减", "type": "bkc", "req_code": "bkc.881553"}, {"code": "881558", "name": "深成500", "type": "bkc", "req_code": "bkc.881558"}, {"code": "881562", "name": "股权激励", "type": "bkc", "req_code": "bkc.881562"}, {"code": "881563", "name": "滨海新区", "type": "bkc", "req_code": "bkc.881563"}, {"code": "881567", "name": "油价相关", "type": "bkc", "req_code": "bkc.881567"}, {"code": "881571", "name": "基本金属", "type": "bkc", "req_code": "bkc.881571"}, {"code": "881575", "name": "移动支付", "type": "bkc", "req_code": "bkc.881575"}, {"code": "881580", "name": "物联网", "type": "bkc", "req_code": "bkc.881580"}, {"code": "881583", "name": "机构重仓", "type": "bkc", "req_code": "bkc.881583"}, {"code": "881584", "name": "深圳特区", "type": "bkc", "req_code": "bkc.881584"}, {"code": "881585", "name": "生物疫苗", "type": "bkc", "req_code": "bkc.881585"}, {"code": "881586", "name": "黄金概念", "type": "bkc", "req_code": "bkc.881586"}, {"code": "881587", "name": "基金重仓", "type": "bkc", "req_code": "bkc.881587"}, {"code": "881591", "name": "QFII重仓", "type": "bkc", "req_code": "bkc.881591"}, {"code": "881592", "name": "成渝特区", "type": "bkc", "req_code": "bkc.881592"}, {"code": "881595", "name": "转债标的", "type": "bkc", "req_code": "bkc.881595"}, {"code": "881598", "name": "参股银行", "type": "bkc", "req_code": "bkc.881598"}, {"code": "881601", "name": "参股期货", "type": "bkc", "req_code": "bkc.881601"}, {"code": "881606", "name": "新材料", "type": "bkc", "req_code": "bkc.881606"}, {"code": "881610", "name": "社保重仓", "type": "bkc", "req_code": "bkc.881610"}, {"code": "881612", "name": "稀缺资源", "type": "bkc", "req_code": "bkc.881612"}, {"code": "881616", "name": "参股券商", "type": "bkc", "req_code": "bkc.881616"}, {"code": "881621", "name": "化工原料", "type": "bkc", "req_code": "bkc.881621"}, {"code": "881623", "name": "ST股", "type": "bkc", "req_code": "bkc.881623"}, {"code": "881624", "name": "网络游戏", "type": "bkc", "req_code": "bkc.881624"}, {"code": "881626", "name": "创投", "type": "bkc", "req_code": "bkc.881626"}, {"code": "881631", "name": "中字头", "type": "bkc", "req_code": "bkc.881631"}, {"code": "881636", "name": "次新股", "type": "bkc", "req_code": "bkc.881636"}, {"code": "881640", "name": "HS300_", "type": "bkc", "req_code": "bkc.881640"}, {"code": "881645", "name": "AH股", "type": "bkc", "req_code": "bkc.881645"}, {"code": "881650", "name": "AB股", "type": "bkc", "req_code": "bkc.881650"}, {"code": "881655", "name": "节能环保", "type": "bkc", "req_code": "bkc.881655"}, {"code": "881660", "name": "新能源", "type": "bkc", "req_code": "bkc.881660"}, {"code": "881664", "name": "煤化工", "type": "bkc", "req_code": "bkc.881664"}, {"code": "881668", "name": "军工", "type": "bkc", "req_code": "bkc.881668"}, {"code": "881767", "name": "荣耀概念", "type": "bkc", "req_code": "bkc.881767"}, {"code": "881768", "name": "AI眼镜", "type": "bkc", "req_code": "bkc.881768"}, {"code": "881769", "name": "西部大开发", "type": "bkc", "req_code": "bkc.881769"}, {"code": "881770", "name": "房屋检测", "type": "bkc", "req_code": "bkc.881770"}, {"code": "881771", "name": "华为海思", "type": "bkc", "req_code": "bkc.881771"}, {"code": "881772", "name": "并购重组概念", "type": "bkc", "req_code": "bkc.881772"}, {"code": "881773", "name": "智谱AI", "type": "bkc", "req_code": "bkc.881773"}, {"code": "881774", "name": "谷子经济", "type": "bkc", "req_code": "bkc.881774"}, {"code": "881775", "name": "人形机器人", "type": "bkc", "req_code": "bkc.881775"}, {"code": "881776", "name": "首发经济", "type": "bkc", "req_code": "bkc.881776"}, {"code": "881777", "name": "冰雪经济", "type": "bkc", "req_code": "bkc.881777"}, {"code": "881778", "name": "小红书概念", "type": "bkc", "req_code": "bkc.881778"}, {"code": "881779", "name": "AI智能体", "type": "bkc", "req_code": "bkc.881779"}, {"code": "881780", "name": "DeepSeek概念", "type": "bkc", "req_code": "bkc.881780"}, {"code": "881781", "name": "腾讯云", "type": "bkc", "req_code": "bkc.881781"}, {"code": "881782", "name": "虚拟机器人", "type": "bkc", "req_code": "bkc.881782"}]}