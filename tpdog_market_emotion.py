import os
import requests
import json
import time
from datetime import datetime
from dotenv import load_dotenv

# 配置项定义
FORCE_UPDATE = False  # 强制更新开关，默认为False
MARKET_CLOSE_TIME = "15:00"  # 收盘时间定义
RECORD_FILE_NAME = "emotion_fetch_records.json"  # 记录文件名


def load_tpdog_token():
    """
    加载TPDog Token从环境变量
    """
    # 加载 .env 文件中的环境变量
    load_dotenv()

    # 获取TPDOG_TOKEN
    token = os.getenv("TPDOG_TOKEN")

    if not token:
        print("❌ 错误: 未在 .env 文件中找到 TPDOG_TOKEN")
        print("   请在项目根目录创建 .env 文件，并添加: TPDOG_TOKEN=你的token")
        return None

    print(f"✅ 成功加载TPDog Token: {token[:10]}...")
    return token


def is_after_market_close():
    """
    判断当前时间是否为收盘后（15:00后）
    """
    now = datetime.now()
    market_close = datetime.strptime(MARKET_CLOSE_TIME, "%H:%M").time()
    current_time = now.time()

    return current_time > market_close


def get_date_folder():
    """
    获取当前日期文件夹路径
    """
    date_folder = datetime.now().strftime('%Y-%m-%d')
    return os.path.join("fund_flow", date_folder)


def load_fetch_records():
    """
    加载获取记录文件
    """
    records_file = os.path.join(get_date_folder(), RECORD_FILE_NAME)

    if os.path.exists(records_file):
        try:
            with open(records_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠️ 警告: 读取记录文件失败: {e}")
            return {}

    return {}


def save_fetch_records(records):
    """
    保存获取记录文件
    """
    date_dir = get_date_folder()
    os.makedirs(date_dir, exist_ok=True)

    records_file = os.path.join(date_dir, RECORD_FILE_NAME)

    try:
        with open(records_file, 'w', encoding='utf-8') as f:
            json.dump(records, f, ensure_ascii=False, indent=2)
        print(f"📝 获取记录已更新: {records_file}")
    except Exception as e:
        print(f"❌ 保存记录文件失败: {e}")


def update_emotion_record(records, date, status, message=""):
    """
    更新市场情绪数据的记录
    """
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    if date not in records:
        records[date] = {}

    records[date].update({
        'data_type': 'market_emotion',
        'last_fetch_time': timestamp,
        'status': status,  # 'success', 'failed', 'skipped'
        'message': message
    })

    return records


def should_skip_emotion_fetch(records, date):
    """
    判断是否应该跳过市场情绪数据的获取
    - 如果开启强制更新，则不跳过
    - 如果是收盘时间后且本地已有数据，则跳过
    """
    if FORCE_UPDATE:
        return False

    if not is_after_market_close():
        return False

    # 检查是否已有今日记录
    if date in records:
        last_status = records[date].get('status')
        if last_status == 'success':
            return True

    return False


def check_existing_emotion_files(date):
    """
    检查是否已有指定日期的市场情绪数据文件
    """
    date_dir = get_date_folder()

    if not os.path.exists(date_dir):
        return False

    # 检查是否有相关文件
    files = os.listdir(date_dir)
    pattern = "_tpdog_market_emotion_"

    return any(pattern in filename for filename in files)


def check_trading_day(token, date=None):
    """
    检查指定日期是否为交易日

    参数:
    - token: TPDog API Token
    - date: 日期，格式 yyyy-MM-dd，默认为当前日期

    返回:
    - True: 是交易日
    - False: 不是交易日
    - None: 检查失败
    """
    # 如果没有指定日期，使用当前日期
    if date is None:
        date = datetime.now().strftime('%Y-%m-%d')

    # API接口地址
    api_url = "https://www.tpdog.com/api/hs/trading_day/is"

    # 设置请求参数
    params = {
        'date': date,
        'token': token
    }

    try:
        print(f"🔍 正在检查 {date} 是否为交易日...")

        # 发送GET请求
        response = requests.get(api_url, params=params, timeout=10)
        response.raise_for_status()

        # 解析JSON响应
        data = response.json()

        # 检查API响应状态
        if data.get("code") == 1000:
            content = data.get("content", {})
            is_trading = content.get("is_trainding", False)  # 注意API返回的字段名是 is_trainding

            if is_trading:
                print(f"✅ {date} 是交易日")
            else:
                print(f"❌ {date} 不是交易日")

            return is_trading
        else:
            print(f"❌ 检查交易日失败: {data.get('message', '未知错误')}")
            return None

    except Exception as e:
        print(f"❌ 检查交易日失败: {e}")
        return None


def find_last_trading_day(token, start_date=None, max_days=10):
    """
    查找上一个交易日

    参数:
    - token: TPDog API Token
    - start_date: 开始查找的日期，格式 yyyy-MM-dd，默认为当前日期
    - max_days: 最大查找天数，默认10天

    返回:
    - 上一个交易日的日期字符串，格式 yyyy-MM-dd
    - None: 未找到交易日
    """
    from datetime import timedelta

    # 如果没有指定开始日期，使用当前日期
    if start_date is None:
        current_date = datetime.now()
    else:
        current_date = datetime.strptime(start_date, '%Y-%m-%d')

    print(f"🔍 正在查找上一个交易日（从 {current_date.strftime('%Y-%m-%d')} 开始）...")

    # 从前一天开始查找
    check_date = current_date - timedelta(days=1)

    for i in range(max_days):
        date_str = check_date.strftime('%Y-%m-%d')

        # 检查是否为交易日
        is_trading = check_trading_day(token, date_str)

        if is_trading is None:
            print(f"❌ 检查 {date_str} 交易日状态失败")
            return None

        if is_trading:
            print(f"✅ 找到上一个交易日: {date_str}")
            return date_str

        # 继续往前查找
        check_date = check_date - timedelta(days=1)

        # 控制请求频率
        time.sleep(0.1)

    print(f"❌ 在过去 {max_days} 天内未找到交易日")
    return None


def get_market_emotion_data(token, date=None):
    """
    获取市场情绪监控数据（带增量更新功能和交易日检查）

    参数:
    - token: TPDog API Token
    - date: 日期，格式 yyyy-MM-dd，默认为当前日期
    """
    # 如果没有指定日期，使用当前日期
    if date is None:
        date = datetime.now().strftime('%Y-%m-%d')

    print(f"\n📊 开始获取市场情绪监控数据...")
    print(f"🔧 强制更新模式: {'开启' if FORCE_UPDATE else '关闭'}")
    print(f"⏰ 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📈 是否为收盘后: {'是' if is_after_market_close() else '否'}")
    print(f"📅 目标日期: {date}")

    # 1. 首先检查是否为交易日
    is_trading = check_trading_day(token, date)

    if is_trading is None:
        print("❌ 无法确定是否为交易日，程序终止")
        return None

    # 如果不是交易日，查找上一个交易日
    if not is_trading:
        print(f"⚠️ {date} 不是交易日，正在查找上一个交易日...")

        last_trading_day = find_last_trading_day(token, date)

        if last_trading_day is None:
            print("❌ 未找到上一个交易日，程序终止")
            # 记录查找失败状态
            records = load_fetch_records()
            records = update_emotion_record(records, date, 'failed', '非交易日且未找到上一个交易日')
            save_fetch_records(records)
            return None

        print(f"🔄 将获取上一个交易日 {last_trading_day} 的数据")
        # 更新目标日期为上一个交易日
        date = last_trading_day

    # 2. 加载获取记录
    records = load_fetch_records()

    # 3. 检查是否应该跳过
    if should_skip_emotion_fetch(records, date):
        print(f"⏭️ 跳过 {date} 市场情绪数据获取 (已有数据)")
        records = update_emotion_record(records, date, 'skipped', '已有数据，跳过获取')
        save_fetch_records(records)

        # 尝试从本地文件加载数据
        try:
            date_dir = get_date_folder()
            files = os.listdir(date_dir)
            emotion_files = [f for f in files if "_tpdog_market_emotion_raw.json" in f]

            if emotion_files:
                # 取最新的文件
                latest_file = sorted(emotion_files)[-1]
                file_path = os.path.join(date_dir, latest_file)

                with open(file_path, 'r', encoding='utf-8') as f:
                    saved_data = json.load(f)
                    print("📂 已从本地文件加载数据")
                    return saved_data.get('content', {})

        except Exception as e:
            print(f"⚠️ 警告: 读取本地数据失败: {e}")

        return None

    # API接口地址
    api_url = "https://www.tpdog.com/api/hs/emotion/get"

    # 设置请求参数
    params = {
        'date': date,
        'token': token
    }

    try:
        print(f"🔄 正在从API获取 {date} 的市场情绪监控数据...")

        # 发送GET请求
        response = requests.get(api_url, params=params, timeout=10)
        response.raise_for_status()

        # 解析JSON响应
        data = response.json()

        # 检查API响应状态
        if data.get("code") == 1000:
            print("✅ 成功获取市场情绪监控数据")
            # 更新成功记录
            records = update_emotion_record(records, date, 'success', '数据获取成功')
            save_fetch_records(records)
            return data.get("content", {})
        else:
            print(f"❌ 获取市场情绪监控数据失败: {data.get('message', '未知错误')}")
            # 更新失败记录
            records = update_emotion_record(records, date, 'failed', f"API错误: {data.get('message', '未知错误')}")
            save_fetch_records(records)
            return None

    except Exception as e:
        print(f"❌ 获取市场情绪监控数据失败: {e}")
        # 更新失败记录
        records = update_emotion_record(records, date, 'failed', f"网络错误: {str(e)}")
        save_fetch_records(records)
        return None


def print_market_emotion_summary(emotion_data):
    """
    打印市场情绪监控关键信息
    """
    if not emotion_data:
        print("❌ 无有效数据可显示")
        return
    
    print("\n" + "=" * 80)
    print("📊 市场情绪监控数据")
    print("=" * 80)
    
    # 基本信息
    print(f"📅 统计日期: {emotion_data.get('date', 'N/A')}")
    
    # 涨跌统计
    print(f"\n📈 涨跌统计:")
    up_num = emotion_data.get('up_num', 0)
    down_num = emotion_data.get('down_num', 0)
    line_num = emotion_data.get('line_num', 0)
    total_stocks = up_num + down_num + line_num
    
    print(f"  上涨股票: {up_num:,} 只 ({up_num/total_stocks*100:.1f}%)" if total_stocks > 0 else f"  上涨股票: {up_num:,} 只")
    print(f"  下跌股票: {down_num:,} 只 ({down_num/total_stocks*100:.1f}%)" if total_stocks > 0 else f"  下跌股票: {down_num:,} 只")
    print(f"  持平股票: {line_num:,} 只 ({line_num/total_stocks*100:.1f}%)" if total_stocks > 0 else f"  持平股票: {line_num:,} 只")
    print(f"  总计股票: {total_stocks:,} 只")
    
    # 涨跌停统计
    print(f"\n🔥 涨跌停统计:")
    l_up_num = emotion_data.get('l_up_num', 0)
    l_down_num = emotion_data.get('l_down_num', 0)
    f_num = emotion_data.get('f_num', 0)
    
    print(f"  涨停数量: {l_up_num:,} 只")
    print(f"  跌停数量: {l_down_num:,} 只")
    print(f"  炸板数量: {f_num:,} 只")
    
    # 连板统计
    print(f"\n🚀 连板统计:")
    c_num = emotion_data.get('c_num', 0)
    c_snum = emotion_data.get('c_snum', 0)
    c_tnum = emotion_data.get('c_tnum', 0)
    c_more_num = emotion_data.get('c_more_num', 0)
    c_trate = emotion_data.get('c_trate', 0)
    c_mrate = emotion_data.get('c_mrate', 0)
    
    print(f"  连板总数: {c_num:,} 只")
    print(f"  2板数量: {c_snum:,} 只")
    print(f"  3板数量: {c_tnum:,} 只")
    print(f"  3板以上: {c_more_num:,} 只")
    print(f"  3板比率: {c_trate:.2f}%")
    print(f"  3板以上比率: {c_mrate:.2f}%")
    
    # 3板股票详情
    c_ts = emotion_data.get('c_ts', [])
    if c_ts:
        print(f"\n📋 3板股票详情 (共{len(c_ts)}只):")
        print(f"{'序号':<4} {'股票代码':<10} {'股票名称':<15} {'开盘价':<8} {'收盘价':<8} {'最高价':<8} {'最低价':<8} {'交易所':<6}")
        print("-" * 80)
        for i, stock in enumerate(c_ts, 1):
            print(f"{i:<4} {stock.get('code', 'N/A'):<10} {stock.get('name', 'N/A'):<15} "
                  f"{stock.get('open', 0):<8.2f} {stock.get('close', 0):<8.2f} "
                  f"{stock.get('high', 0):<8.2f} {stock.get('low', 0):<8.2f} {stock.get('type', 'N/A'):<6}")
    
    # 3板以上股票详情
    c_ms = emotion_data.get('c_ms', [])
    if c_ms:
        print(f"\n🔥 3板以上股票详情 (共{len(c_ms)}只):")
        print(f"{'序号':<4} {'股票代码':<10} {'股票名称':<15} {'开盘价':<8} {'收盘价':<8} {'最高价':<8} {'最低价':<8} {'交易所':<6}")
        print("-" * 80)
        for i, stock in enumerate(c_ms, 1):
            print(f"{i:<4} {stock.get('code', 'N/A'):<10} {stock.get('name', 'N/A'):<15} "
                  f"{stock.get('open', 0):<8.2f} {stock.get('close', 0):<8.2f} "
                  f"{stock.get('high', 0):<8.2f} {stock.get('low', 0):<8.2f} {stock.get('type', 'N/A'):<6}")
    
    # 市场情绪分析
    print(f"\n💡 市场情绪分析:")
    if total_stocks > 0:
        up_ratio = up_num / total_stocks * 100
        if up_ratio >= 70:
            emotion_level = "🔥 极度乐观"
        elif up_ratio >= 60:
            emotion_level = "😊 乐观"
        elif up_ratio >= 50:
            emotion_level = "😐 中性偏乐观"
        elif up_ratio >= 40:
            emotion_level = "😟 中性偏悲观"
        elif up_ratio >= 30:
            emotion_level = "😰 悲观"
        else:
            emotion_level = "💀 极度悲观"
        
        print(f"  市场情绪: {emotion_level}")
        print(f"  上涨比例: {up_ratio:.1f}%")
    
    if l_up_num > 0:
        limit_up_ratio = l_up_num / total_stocks * 100 if total_stocks > 0 else 0
        print(f"  涨停比例: {limit_up_ratio:.2f}%")
        
        if limit_up_ratio >= 5:
            limit_emotion = "🚀 超强势"
        elif limit_up_ratio >= 3:
            limit_emotion = "💪 强势"
        elif limit_up_ratio >= 1:
            limit_emotion = "👍 偏强"
        else:
            limit_emotion = "😴 平淡"
        
        print(f"  涨停情绪: {limit_emotion}")
    
    print("=" * 80)


def save_market_emotion_data(emotion_data, data_date=None):
    """
    保存市场情绪监控原始数据到fund_flow文件夹，按照日期保存

    参数:
    - emotion_data: 市场情绪数据
    - data_date: 数据对应的日期，如果为None则使用当前日期
    """
    if not emotion_data:
        print("❌ 无数据可保存")
        return

    # 如果没有指定数据日期，使用当前日期
    if data_date is None:
        data_date = datetime.now().strftime('%Y-%m-%d')

    now = datetime.now()
    timestamp = now.strftime('%H-%M')

    # 创建fund_flow文件夹和日期子文件夹（按当前日期创建文件夹）
    date_dir = get_date_folder()
    os.makedirs(date_dir, exist_ok=True)
    
    try:
        # 保存原始数据JSON文件
        raw_filename = f"{timestamp}_tpdog_market_emotion_{data_date}_raw.json"
        raw_filepath = os.path.join(date_dir, raw_filename)

        # 构建完整的保存数据
        save_data = {
            'timestamp': now.strftime('%Y-%m-%d %H:%M:%S'),
            'data_date': data_date,  # 数据对应的日期
            'fetch_date': now.strftime('%Y-%m-%d'),  # 获取数据的日期
            'data_type': 'market_emotion',
            'content': emotion_data
        }
        
        with open(raw_filepath, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 市场情绪监控原始数据已保存: {raw_filepath}")
        
        # 保存统计摘要文件
        summary_filename = f"{timestamp}_tpdog_market_emotion_{data_date}_summary.txt"
        summary_filepath = os.path.join(date_dir, summary_filename)

        save_emotion_summary_to_file(emotion_data, summary_filepath, data_date)
        print(f"💾 市场情绪监控摘要已保存: {summary_filepath}")
        
        print(f"📁 所有文件已保存到目录: {date_dir}")
        
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
        import traceback
        traceback.print_exc()


def save_emotion_summary_to_file(emotion_data, filepath, date):
    """
    保存市场情绪监控摘要到文本文件
    """
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"TPDog 市场情绪监控摘要\n")
            f.write("=" * 50 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"统计日期: {date}\n\n")
            
            # 基本统计
            up_num = emotion_data.get('up_num', 0)
            down_num = emotion_data.get('down_num', 0)
            line_num = emotion_data.get('line_num', 0)
            total_stocks = up_num + down_num + line_num
            
            f.write(f"涨跌统计:\n")
            f.write(f"  上涨股票: {up_num:,} 只\n")
            f.write(f"  下跌股票: {down_num:,} 只\n")
            f.write(f"  持平股票: {line_num:,} 只\n")
            f.write(f"  总计股票: {total_stocks:,} 只\n")
            
            if total_stocks > 0:
                f.write(f"  上涨比例: {up_num/total_stocks*100:.1f}%\n")
                f.write(f"  下跌比例: {down_num/total_stocks*100:.1f}%\n")
            
            # 涨跌停统计
            l_up_num = emotion_data.get('l_up_num', 0)
            l_down_num = emotion_data.get('l_down_num', 0)
            f_num = emotion_data.get('f_num', 0)
            
            f.write(f"\n涨跌停统计:\n")
            f.write(f"  涨停数量: {l_up_num:,} 只\n")
            f.write(f"  跌停数量: {l_down_num:,} 只\n")
            f.write(f"  炸板数量: {f_num:,} 只\n")
            
            # 连板统计
            c_num = emotion_data.get('c_num', 0)
            c_snum = emotion_data.get('c_snum', 0)
            c_tnum = emotion_data.get('c_tnum', 0)
            c_more_num = emotion_data.get('c_more_num', 0)
            
            f.write(f"\n连板统计:\n")
            f.write(f"  连板总数: {c_num:,} 只\n")
            f.write(f"  2板数量: {c_snum:,} 只\n")
            f.write(f"  3板数量: {c_tnum:,} 只\n")
            f.write(f"  3板以上: {c_more_num:,} 只\n")
            f.write(f"  3板比率: {emotion_data.get('c_trate', 0):.2f}%\n")
            f.write(f"  3板以上比率: {emotion_data.get('c_mrate', 0):.2f}%\n")
            
            # 3板股票列表
            c_ts = emotion_data.get('c_ts', [])
            if c_ts:
                f.write(f"\n3板股票列表:\n")
                for i, stock in enumerate(c_ts, 1):
                    f.write(f"  {i}. {stock.get('name', 'N/A')}({stock.get('code', 'N/A')}) "
                           f"收盘价: {stock.get('close', 0):.2f}\n")
            
            # 3板以上股票列表
            c_ms = emotion_data.get('c_ms', [])
            if c_ms:
                f.write(f"\n3板以上股票列表:\n")
                for i, stock in enumerate(c_ms, 1):
                    f.write(f"  {i}. {stock.get('name', 'N/A')}({stock.get('code', 'N/A')}) "
                           f"收盘价: {stock.get('close', 0):.2f}\n")
                           
    except Exception as e:
        print(f"❌ 保存摘要文件失败: {e}")


def print_fetch_records():
    """
    打印获取记录统计
    """
    records = load_fetch_records()

    if not records:
        print("📝 暂无获取记录")
        return

    print("\n" + "=" * 60)
    print("📝 今日市场情绪数据获取记录")
    print("=" * 60)

    success_count = 0
    failed_count = 0
    skipped_count = 0

    for date, record in records.items():
        status = record.get('status', 'unknown')
        if status == 'success':
            success_count += 1
        elif status == 'failed':
            failed_count += 1
        elif status == 'skipped':
            skipped_count += 1

    print(f"✅ 成功获取: {success_count} 次")
    print(f"❌ 获取失败: {failed_count} 次")
    print(f"⏭️ 跳过获取: {skipped_count} 次")
    print(f"📊 总计记录: {len(records)} 次")

    # 显示详细记录
    print(f"\n详细记录:")
    print("-" * 60)

    sorted_records = sorted(records.items(),
                            key=lambda x: x[1].get('last_fetch_time', ''),
                            reverse=True)

    for date, record in sorted_records:
        status_icon = {'success': '✅', 'failed': '❌', 'skipped': '⏭️'}.get(record.get('status'), '❓')
        print(f"{status_icon} {date:<12} {record.get('last_fetch_time', 'N/A'):<20} {record.get('message', 'N/A')}")


def clear_emotion_cache():
    """
    清理市场情绪数据缓存和记录
    """
    try:
        records_file = os.path.join(get_date_folder(), RECORD_FILE_NAME)
        if os.path.exists(records_file):
            os.remove(records_file)
            print(f"🗑️ 获取记录文件已清理: {records_file}")
        else:
            print("📝 无获取记录文件需要清理")
    except Exception as e:
        print(f"❌ 清理记录文件失败: {e}")


def main():
    """
    主函数
    """
    global FORCE_UPDATE

    print("🚀 TPDog 市场情绪监控数据获取工具启动...")
    print(f"🔧 强制更新模式: {'开启' if FORCE_UPDATE else '关闭'}")

    # 1. 加载Token
    token = load_tpdog_token()
    if not token:
        return

    # 2. 选择运行模式
    print("\n请选择运行模式:")
    print("1. 获取市场情绪监控数据（自动处理交易日）")
    print("2. 获取指定日期的市场情绪数据")
    print("3. 查看今日获取记录")
    print("4. 检查指定日期是否为交易日")
    print("5. 切换强制更新模式")
    print("6. 清理获取记录")

    try:
        choice = input("请输入选择 (1-6): ").strip()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
        return

    if choice == "1":
        # 获取市场情绪监控数据（自动处理交易日）
        emotion_data = get_market_emotion_data(token)

        if emotion_data:
            # 打印关键信息
            print_market_emotion_summary(emotion_data)

            # 保存原始数据（传入实际的数据日期）
            actual_date = emotion_data.get('date', datetime.now().strftime('%Y-%m-%d'))
            save_market_emotion_data(emotion_data, actual_date)
        else:
            print("❌ 未获取到有效的市场情绪监控数据")

    elif choice == "2":
        # 获取指定日期的市场情绪数据
        try:
            target_date = input("请输入要获取的日期 (格式: YYYY-MM-DD，回车使用今日): ").strip()
            if not target_date:
                target_date = datetime.now().strftime('%Y-%m-%d')

            # 验证日期格式
            try:
                datetime.strptime(target_date, '%Y-%m-%d')
            except ValueError:
                print("❌ 日期格式错误，请使用 YYYY-MM-DD 格式")
                return

            emotion_data = get_market_emotion_data(token, target_date)

            if emotion_data:
                # 打印关键信息
                print_market_emotion_summary(emotion_data)

                # 保存原始数据（传入实际的数据日期）
                actual_date = emotion_data.get('date', target_date)
                save_market_emotion_data(emotion_data, actual_date)
            else:
                print("❌ 未获取到有效的市场情绪监控数据")
        except KeyboardInterrupt:
            print("\n👋 已取消操作")

    elif choice == "3":
        # 查看今日获取记录
        print_fetch_records()

    elif choice == "4":
        # 检查指定日期是否为交易日
        try:
            check_date = input("请输入要检查的日期 (格式: YYYY-MM-DD，回车使用今日): ").strip()
            if not check_date:
                check_date = datetime.now().strftime('%Y-%m-%d')

            # 验证日期格式
            try:
                datetime.strptime(check_date, '%Y-%m-%d')
            except ValueError:
                print("❌ 日期格式错误，请使用 YYYY-MM-DD 格式")
                return

            is_trading = check_trading_day(token, check_date)
            if is_trading is not None:
                if is_trading:
                    print(f"✅ {check_date} 是交易日，可以获取市场情绪数据")
                else:
                    print(f"❌ {check_date} 不是交易日，无市场情绪数据")
        except KeyboardInterrupt:
            print("\n👋 已取消操作")

    elif choice == "5":
        # 切换强制更新模式
        current_mode = FORCE_UPDATE
        FORCE_UPDATE = not current_mode
        print(f"🔧 强制更新模式已{'开启' if FORCE_UPDATE else '关闭'}")
        print("⚠️ 注意: 此设置仅在当前运行中生效，重启程序后恢复默认设置")
        # 返回主菜单
        main()
        return

    elif choice == "6":
        # 清理获取记录
        try:
            confirm = input("确认清理所有获取记录？(y/N): ").strip().lower()
            if confirm == 'y':
                clear_emotion_cache()
            else:
                print("⏭️ 已取消清理操作")
        except KeyboardInterrupt:
            print("\n⏭️ 已取消清理操作")

    else:
        print("❌ 无效选择，程序退出")
        return

    print(f"\n{'=' * 50}")
    print("💡 提示:")
    print("   1. 数据来源于TPDog API，更新频率为交易日收盘后")
    print("   2. 请确保在 .env 文件中正确设置了 TPDOG_TOKEN")
    print("   3. API限制为30次/秒")
    print("   4. 数据已按日期保存到 fund_flow 文件夹中")
    print("   5. 保存的文件包括:")
    print("      - 原始数据 (JSON)")
    print("      - 统计摘要 (TXT)")
    print("      - 获取记录 (JSON)")
    print(f"   6. 强制更新模式: {'开启' if FORCE_UPDATE else '关闭'}")
    print("   7. 收盘后(15:00后)会自动跳过已获取的数据")
    print("   8. 每次运行都会记录获取状态和时间")
    print("   9. 增量更新功能可避免重复获取相同数据")
    print("  10. 自动检查交易日，非交易日会自动获取上一个交易日数据")
    print("  11. 支持指定日期获取历史市场情绪数据")


if __name__ == "__main__":
    main()
