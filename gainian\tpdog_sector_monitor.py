#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TPDOG版块资金流监控程序
专门获取TPDOG的概念、行业资金流数据

功能：
1. 收盘时获取版块列表（如果不存在）
2. 交易时间获取版块资金流数据
3. 获取前10版块的个股资金流
4. 获取版块实时行情数据
5. 定时保存数据到本地

开放时间：交易日09:30~11:30/13:00~15:00
"""

import requests
import pandas as pd
import schedule
import time as time_module
from datetime import datetime, time
from dotenv import load_dotenv
import os
import logging
import json
import threading
import warnings

# 忽略pandas的FutureWarning
warnings.filterwarnings("ignore", category=FutureWarning, module="pandas")

# 加载环境变量
load_dotenv()

# 配置日志
def setup_logging():
    """设置日志配置"""
    today_str = datetime.now().strftime('%Y%m%d')
    log_dir = os.path.join('data', today_str)
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'tpdog_monitor.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

# 全局配置
TPDOG_TOKEN = os.getenv('TPDOG_TOKEN')
if not TPDOG_TOKEN:
    print("⚠️ 警告：未在.env文件中配置TPDOG_TOKEN，程序无法运行")
    print("   请在.env文件中设置: TPDOG_TOKEN=your_token")
    exit(1)

# 交易时间配置
TRADING_START_1 = time(9, 30)
TRADING_END_1 = time(11, 30)
TRADING_START_2 = time(13, 0)
TRADING_END_2 = time(15, 0)

def is_trading_time():
    """检查是否为交易时间"""
    now = datetime.now().time()
    return (TRADING_START_1 <= now <= TRADING_END_1) or (TRADING_START_2 <= now <= TRADING_END_2)

def get_date_folder():
    """获取今日数据文件夹路径"""
    today_str = datetime.now().strftime('%Y%m%d')
    date_folder = os.path.join('data', today_str)
    os.makedirs(date_folder, exist_ok=True)
    return date_folder

def get_timestamp():
    """获取时间戳字符串"""
    return datetime.now().strftime('%Y%m%d_%H%M%S')

def format_amount(amount):
    """格式化金额显示"""
    if not isinstance(amount, (int, float)):
        return str(amount)
    if abs(amount) >= 1_0000_0000:
        return f"{amount / 1_0000_0000:.2f}亿"
    elif abs(amount) >= 1_0000:
        return f"{amount / 1_0000:.2f}万"
    else:
        return f"{amount:.2f}"

def get_tpdog_sector_list(sector_type, token):
    """
    获取TPDOG版块列表
    
    Args:
        sector_type: 版块类型 ('bki': 行业版块, 'bkc': 概念版块, 'bkr': 地域版块)
        token: TPDOG API token
    
    Returns:
        DataFrame: 版块列表数据，失败返回None
    """
    try:
        url = f"https://www.tpdog.com/api/bk/list?type={sector_type}&token={token}"
        
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 1000 and data.get('content'):
                df = pd.DataFrame(data['content'])
                print(f"✅ 成功获取{sector_type}版块列表: {len(df)} 个版块")
                logging.info(f"成功获取{sector_type}版块列表: {len(df)} 个版块")
                return df
            else:
                raise ValueError(f"TPDOG版块列表API返回错误: {data}")
        else:
            raise ValueError(f"TPDOG版块列表API请求失败，状态码: {response.status_code}")
            
    except Exception as e:
        logging.error(f"获取{sector_type}版块列表失败: {e}")
        print(f"❌ 获取{sector_type}版块列表失败: {e}")
        return None

def get_tpdog_sector_funds(sector_type, token, field='m_net', sort=2):
    """
    获取TPDOG版块资金流数据
    
    Args:
        sector_type: 版块类型 ('bki': 行业版块, 'bkc': 概念版块, 'bkr': 地域版块)
        token: TPDOG API token
        field: 排序字段，默认按主力净流入排序
        sort: 排序方式，2为倒序（从大到小）
    
    Returns:
        DataFrame: 版块资金流数据，失败返回None
    """
    try:
        if not is_trading_time():
            print(f"⚠️ 非交易时间，跳过{sector_type}版块资金流获取")
            return None
        
        url = f"https://www.tpdog.com/api/hs/current/bk_funds?bk_type={sector_type}&field={field}&sort={sort}&token={token}"
        
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 1000 and data.get('content'):
                df = pd.DataFrame(data['content'])
                print(f"✅ 成功获取{sector_type}版块资金流数据: {len(df)} 条")
                logging.info(f"成功获取{sector_type}版块资金流数据: {len(df)} 条")
                return df
            else:
                raise ValueError(f"TPDOG版块资金流API返回错误: {data}")
        else:
            raise ValueError(f"TPDOG版块资金流API请求失败，状态码: {response.status_code}")
            
    except Exception as e:
        logging.error(f"获取{sector_type}版块资金流失败: {e}")
        print(f"❌ 获取{sector_type}版块资金流失败: {e}")
        return None

def get_tpdog_sector_stocks(sector_code, token):
    """
    获取TPDOG版块内个股列表
    
    Args:
        sector_code: 版块代码，如 'bki.880158'
        token: TPDOG API token
    
    Returns:
        DataFrame: 个股列表数据，失败返回None
    """
    try:
        url = f"https://www.tpdog.com/api/hs/stocks/list_board?code={sector_code}&token={token}"
        
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 1000 and data.get('content'):
                df = pd.DataFrame(data['content'])
                print(f"✅ 成功获取版块{sector_code}个股列表: {len(df)} 只股票")
                logging.info(f"成功获取版块{sector_code}个股列表: {len(df)} 只股票")
                return df
            else:
                raise ValueError(f"TPDOG版块个股API返回错误: {data}")
        else:
            raise ValueError(f"TPDOG版块个股API请求失败，状态码: {response.status_code}")
            
    except Exception as e:
        logging.error(f"获取版块{sector_code}个股列表失败: {e}")
        print(f"❌ 获取版块{sector_code}个股列表失败: {e}")
        return None

def get_tpdog_sector_quote(sector_code, token):
    """
    获取TPDOG版块实时行情数据
    
    Args:
        sector_code: 版块代码，如 'bki.880158'
        token: TPDOG API token
    
    Returns:
        dict: 版块行情数据，失败返回None
    """
    try:
        if not is_trading_time():
            # 非交易时间使用测试参数
            url = f"https://www.tpdog.com/api/hs/current/inventory?code={sector_code}&t=1&token={token}"
        else:
            url = f"https://www.tpdog.com/api/hs/current/inventory?code={sector_code}&token={token}"
        
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 1000 and data.get('content'):
                return data['content']
            elif data.get('code') == 1002:
                # 示例数据，非交易时间返回的测试数据
                print(f"⚠️ 版块{sector_code}返回示例数据（非交易时间）")
                return data.get('content')
            else:
                raise ValueError(f"TPDOG版块行情API返回错误: {data}")
        else:
            raise ValueError(f"TPDOG版块行情API请求失败，状态码: {response.status_code}")
            
    except Exception as e:
        logging.error(f"获取版块{sector_code}行情失败: {e}")
        print(f"❌ 获取版块{sector_code}行情失败: {e}")
        return None

def check_and_update_sector_lists():
    """
    检查并更新版块列表（收盘时执行）
    如果版块列表文件不存在，则获取并保存
    """
    try:
        date_folder = get_date_folder()
        timestamp = get_timestamp()

        # 检查行业版块列表
        industry_list_file = os.path.join(date_folder, 'tpdog_industry_list.json')
        if not os.path.exists(industry_list_file):
            print("🔄 获取行业版块列表...")
            industry_df = get_tpdog_sector_list('bki', TPDOG_TOKEN)
            if industry_df is not None:
                # 保存JSON缓存文件（用于判断是否需要重新获取）
                industry_df.to_json(industry_list_file, orient='records', indent=2)
                print(f"✅ 行业版块列表已保存: {len(industry_df)} 个版块")
                logging.info(f"行业版块列表已保存到: {industry_list_file}")
        else:
            print("✅ 行业版块列表已存在，跳过获取")

        # 检查概念版块列表
        concept_list_file = os.path.join(date_folder, 'tpdog_concept_list.json')
        if not os.path.exists(concept_list_file):
            print("🔄 获取概念版块列表...")
            concept_df = get_tpdog_sector_list('bkc', TPDOG_TOKEN)
            if concept_df is not None:
                # 保存JSON缓存文件（用于判断是否需要重新获取）
                concept_df.to_json(concept_list_file, orient='records', indent=2)
                print(f"✅ 概念版块列表已保存: {len(concept_df)} 个版块")
                logging.info(f"概念版块列表已保存到: {concept_list_file}")
        else:
            print("✅ 概念版块列表已存在，跳过获取")

    except Exception as e:
        logging.error(f"检查更新版块列表失败: {e}")
        print(f"❌ 检查更新版块列表失败: {e}")

def get_sector_funds_data():
    """
    获取版块资金流数据（交易时间执行）
    """
    try:
        if not is_trading_time():
            print("⚠️ 非交易时间，跳过版块资金流获取")
            return

        date_folder = get_date_folder()
        timestamp = get_timestamp()

        print("\n" + "=" * 50)
        print("📊 开始获取TPDOG版块资金流数据")
        print("=" * 50)

        # 1. 获取行业版块资金流
        print("🔄 获取行业版块资金流...")
        industry_funds_df = get_tpdog_sector_funds('bki', TPDOG_TOKEN)
        if industry_funds_df is not None:
            # 保存完整数据
            industry_funds_file = os.path.join(date_folder, f'tpdog_industry_funds_{timestamp}.csv')
            industry_funds_df.to_csv(industry_funds_file, index=False, encoding='utf-8-sig')

            # 打印前10名
            print("\n📈 行业版块资金流前10名:")
            top_10_industry = industry_funds_df.head(10)
            for idx, row in top_10_industry.iterrows():
                print(f"  {idx+1:2d}. {row['name']:15s} | 主力净流入: {format_amount(row['m_net']):>10s} | 净流入比例: {row.get('m_in_ratio', 0):6.2f}%")

            logging.info(f"行业版块资金流数据已保存: {industry_funds_file}")

        # 2. 获取概念版块资金流
        print("\n🔄 获取概念版块资金流...")
        concept_funds_df = get_tpdog_sector_funds('bkc', TPDOG_TOKEN)
        if concept_funds_df is not None:
            # 保存完整数据
            concept_funds_file = os.path.join(date_folder, f'tpdog_concept_funds_{timestamp}.csv')
            concept_funds_df.to_csv(concept_funds_file, index=False, encoding='utf-8-sig')

            # 打印前10名
            print("\n📈 概念版块资金流前10名:")
            top_10_concept = concept_funds_df.head(10)
            for idx, row in top_10_concept.iterrows():
                print(f"  {idx+1:2d}. {row['name']:15s} | 主力净流入: {format_amount(row['m_net']):>10s} | 净流入比例: {row.get('m_in_ratio', 0):6.2f}%")

            logging.info(f"概念版块资金流数据已保存: {concept_funds_file}")

        # 3. 获取前10版块的个股数据
        get_top_sectors_stocks_data(industry_funds_df, concept_funds_df)

        # 4. 获取前10版块的行情数据
        get_top_sectors_quotes_data(industry_funds_df, concept_funds_df)

        print("=" * 50)
        print("✅ TPDOG版块数据获取完成")
        print("=" * 50)

    except Exception as e:
        logging.error(f"获取版块资金流数据失败: {e}")
        print(f"❌ 获取版块资金流数据失败: {e}")

def get_top_sectors_stocks_data(industry_df, concept_df):
    """
    获取前10版块的个股数据
    """
    try:
        import re
        date_folder = get_date_folder()
        timestamp = get_timestamp()

        print("\n🔄 获取前10版块的个股数据...")

        # 处理行业版块前10
        if industry_df is not None and not industry_df.empty:
            top_10_industry = industry_df.head(10)
            for idx, row in top_10_industry.iterrows():
                sector_code = f"bki.{row['code']}"
                sector_name = row['name']

                print(f"  📋 获取行业版块 '{sector_name}' 的个股数据...")
                stocks_df = get_tpdog_sector_stocks(sector_code, TPDOG_TOKEN)

                if stocks_df is not None and not stocks_df.empty:
                    # 清理文件名中的非法字符
                    safe_name = re.sub(r'[\\/*?:"<>|]', "", sector_name)
                    stocks_file = os.path.join(date_folder, f'tpdog_industry_stocks_{safe_name}_{timestamp}.csv')
                    stocks_df.to_csv(stocks_file, index=False, encoding='utf-8-sig')
                    print(f"    ✅ 已保存 {len(stocks_df)} 只个股数据到: {stocks_file}")
                    logging.info(f"行业版块'{sector_name}'个股数据已保存: {stocks_file}")

                # 避免请求过于频繁
                time_module.sleep(0.5)

        # 处理概念版块前10
        if concept_df is not None and not concept_df.empty:
            top_10_concept = concept_df.head(10)
            for idx, row in top_10_concept.iterrows():
                sector_code = f"bkc.{row['code']}"
                sector_name = row['name']

                print(f"  📋 获取概念版块 '{sector_name}' 的个股数据...")
                stocks_df = get_tpdog_sector_stocks(sector_code, TPDOG_TOKEN)

                if stocks_df is not None and not stocks_df.empty:
                    # 清理文件名中的非法字符
                    safe_name = re.sub(r'[\\/*?:"<>|]', "", sector_name)
                    stocks_file = os.path.join(date_folder, f'tpdog_concept_stocks_{safe_name}_{timestamp}.csv')
                    stocks_df.to_csv(stocks_file, index=False, encoding='utf-8-sig')
                    print(f"    ✅ 已保存 {len(stocks_df)} 只个股数据到: {stocks_file}")
                    logging.info(f"概念版块'{sector_name}'个股数据已保存: {stocks_file}")

                # 避免请求过于频繁
                time_module.sleep(0.5)

    except Exception as e:
        logging.error(f"获取前10版块个股数据失败: {e}")
        print(f"❌ 获取前10版块个股数据失败: {e}")

def get_top_sectors_quotes_data(industry_df, concept_df):
    """
    获取前10版块的行情数据
    """
    try:
        date_folder = get_date_folder()
        timestamp = get_timestamp()

        print("\n🔄 获取前10版块的行情数据...")

        all_quotes = []

        # 处理行业版块前10
        if industry_df is not None and not industry_df.empty:
            top_10_industry = industry_df.head(10)
            for idx, row in top_10_industry.iterrows():
                sector_code = f"bki.{row['code']}"
                sector_name = row['name']

                quote_data = get_tpdog_sector_quote(sector_code, TPDOG_TOKEN)
                if quote_data:
                    quote_data['sector_type'] = '行业'
                    quote_data['sector_name'] = sector_name
                    quote_data['sector_code'] = sector_code
                    all_quotes.append(quote_data)

                # 避免请求过于频繁
                time_module.sleep(0.3)

        # 处理概念版块前10
        if concept_df is not None and not concept_df.empty:
            top_10_concept = concept_df.head(10)
            for idx, row in top_10_concept.iterrows():
                sector_code = f"bkc.{row['code']}"
                sector_name = row['name']

                quote_data = get_tpdog_sector_quote(sector_code, TPDOG_TOKEN)
                if quote_data:
                    quote_data['sector_type'] = '概念'
                    quote_data['sector_name'] = sector_name
                    quote_data['sector_code'] = sector_code
                    all_quotes.append(quote_data)

                # 避免请求过于频繁
                time_module.sleep(0.3)

        # 保存所有行情数据
        if all_quotes:
            quotes_df = pd.DataFrame(all_quotes)
            quotes_file = os.path.join(date_folder, f'tpdog_sectors_quotes_{timestamp}.csv')
            quotes_df.to_csv(quotes_file, index=False, encoding='utf-8-sig')

            # 打印涨幅前10的版块
            if 'raise_rate' in quotes_df.columns:
                quotes_df['raise_rate'] = pd.to_numeric(quotes_df['raise_rate'], errors='coerce')
                top_gainers = quotes_df.nlargest(10, 'raise_rate')

                print("\n📈 涨幅前10的版块:")
                for idx, row in top_gainers.iterrows():
                    print(f"  {idx+1:2d}. {row['sector_name']:15s} ({row['sector_type']}) | 涨跌幅: {row['raise_rate']:6.2f}% | 最新价: {row.get('price', 0):8.2f}")

            print(f"✅ 版块行情数据已保存: {quotes_file} ({len(all_quotes)} 个版块)")
            logging.info(f"版块行情数据已保存: {quotes_file}")

    except Exception as e:
        logging.error(f"获取前10版块行情数据失败: {e}")
        print(f"❌ 获取前10版块行情数据失败: {e}")

def run_threaded(job_func):
    """在后台线程中运行任务"""
    job_thread = threading.Thread(target=job_func)
    job_thread.daemon = True
    job_thread.start()

def schedule_jobs():
    """设置定时任务"""
    # 任务1: 收盘后检查版块列表（每日15:10执行）
    schedule.every().day.at("15:10").do(run_threaded, check_and_update_sector_lists)

    # 任务2: 交易时间每2分钟获取版块资金流数据
    schedule.every(2).minutes.do(run_threaded, get_sector_funds_data)

    print("✅ 定时任务已设置:")
    print("   - 每日15:10检查更新版块列表")
    print("   - 每2分钟获取版块资金流数据（仅交易时间）")
    logging.info("定时任务已设置: 每日15:10检查版块列表，每2分钟获取资金流数据")

def main():
    """主程序"""
    print("=" * 60)
    print("🚀 TPDOG版块资金流监控程序启动")
    print("=" * 60)
    print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"交易时间: 09:30-11:30 / 13:00-15:00")
    print(f"数据获取频率: 每2分钟")
    print("=" * 60)

    # 设置日志
    setup_logging()

    # 首次启动时立即执行检查
    print("\n🔄 首次启动，立即执行版块列表检查...")
    check_and_update_sector_lists()

    # 如果是交易时间，立即执行一次数据获取
    if is_trading_time():
        print("\n🔄 当前为交易时间，立即执行一次数据获取...")
        get_sector_funds_data()
    else:
        print("\n⏰ 当前非交易时间，等待交易时间开始...")

    # 设置定时任务
    schedule_jobs()

    # 主循环
    print("\n🔄 进入监控循环，按Ctrl+C退出...")
    try:
        while True:
            schedule.run_pending()
            time_module.sleep(30)  # 每30秒检查一次定时任务
    except KeyboardInterrupt:
        print("\n\n👋 程序已停止")
        logging.info("程序手动停止")

if __name__ == "__main__":
    main()
