#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TPDOG版块资金流监控程序
专门获取TPDOG的概念、行业资金流数据

功能：
1. 收盘时获取版块列表（如果不存在）
2. 交易时间获取版块资金流数据
3. 获取前10版块的个股资金流
4. 获取版块实时行情数据
5. 定时保存数据到本地

开放时间：交易日09:30~11:30/13:00~15:00
"""

import requests
import pandas as pd
import schedule
import time as time_module
from datetime import datetime, time
from dotenv import load_dotenv
import os
import logging
import json
import threading
import warnings

# 忽略pandas的FutureWarning
warnings.filterwarnings("ignore", category=FutureWarning, module="pandas")

# 加载环境变量
load_dotenv()

# 配置日志
def setup_logging():
    """设置日志配置"""
    today_str = datetime.now().strftime('%Y%m%d')
    log_dir = os.path.join('data', today_str)
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'tpdog_monitor.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

# 全局配置
TPDOG_TOKEN = os.getenv('TPDOG_TOKEN')
if not TPDOG_TOKEN:
    print("⚠️ 警告：未在.env文件中配置TPDOG_TOKEN，程序无法运行")
    print("   请在.env文件中设置: TPDOG_TOKEN=your_token")
    exit(1)

# 交易时间配置
TRADING_START_1 = time(9, 30)
TRADING_END_1 = time(11, 30)
TRADING_START_2 = time(13, 0)
TRADING_END_2 = time(15, 0)

def is_trading_time():
    """检查是否为交易时间"""
    now = datetime.now().time()
    return (TRADING_START_1 <= now <= TRADING_END_1) or (TRADING_START_2 <= now <= TRADING_END_2)

def get_date_folder():
    """获取今日数据文件夹路径"""
    today_str = datetime.now().strftime('%Y%m%d')
    date_folder = os.path.join('data', today_str)
    os.makedirs(date_folder, exist_ok=True)
    return date_folder

def get_timestamp():
    """获取时间戳字符串"""
    return datetime.now().strftime('%Y%m%d_%H%M%S')

def format_amount(amount):
    """格式化金额显示"""
    if not isinstance(amount, (int, float)):
        return str(amount)
    if abs(amount) >= 1_0000_0000:
        return f"{amount / 1_0000_0000:.2f}亿"
    elif abs(amount) >= 1_0000:
        return f"{amount / 1_0000:.2f}万"
    else:
        return f"{amount:.2f}"

def get_english_sector_name(chinese_name):
    """
    将中文版块名转换为英文文件名
    """
    # 常见版块名称映射
    sector_mapping = {
        # 行业版块
        '生物制品': 'Biological_Products',
        '医疗器械': 'Medical_Equipment',
        '化学制药': 'Chemical_Pharmaceuticals',
        '中药': 'Traditional_Chinese_Medicine',
        '医疗服务': 'Medical_Services',
        '银行': 'Banking',
        '保险': 'Insurance',
        '证券': 'Securities',
        '房地产开发': 'Real_Estate_Development',
        '物业管理': 'Property_Management',
        '电子制造': 'Electronics_Manufacturing',
        '半导体': 'Semiconductor',
        '计算机设备': 'Computer_Equipment',
        '软件开发': 'Software_Development',
        '通信设备': 'Communication_Equipment',
        '汽车整车': 'Automobile',
        '汽车零部件': 'Auto_Parts',
        '新能源车': 'New_Energy_Vehicle',
        '电力': 'Electric_Power',
        '煤炭': 'Coal',
        '石油石化': 'Oil_Petrochemical',
        '钢铁': 'Steel',
        '有色金属': 'Non_Ferrous_Metals',
        '建筑材料': 'Building_Materials',
        '机械设备': 'Machinery_Equipment',
        '食品饮料': 'Food_Beverage',
        '纺织服装': 'Textile_Apparel',
        '家用电器': 'Home_Appliances',
        '商业贸易': 'Commercial_Trade',
        '交通运输': 'Transportation',
        '旅游酒店': 'Tourism_Hotel',
        '传媒': 'Media',
        '教育': 'Education',
        '农林牧渔': 'Agriculture_Forestry_Animal_Husbandry_Fishery',

        # 概念版块
        'PCB': 'PCB',
        '3D玻璃': '3D_Glass',
        '蓝宝石': 'Sapphire',
        '人工智能': 'Artificial_Intelligence',
        '大数据': 'Big_Data',
        '云计算': 'Cloud_Computing',
        '物联网': 'Internet_of_Things',
        '5G': '5G',
        '新能源': 'New_Energy',
        '锂电池': 'Lithium_Battery',
        '光伏': 'Photovoltaic',
        '风电': 'Wind_Power',
        '氢能源': 'Hydrogen_Energy',
        '碳纤维': 'Carbon_Fiber',
        '芯片': 'Chip',
        '集成电路': 'Integrated_Circuit',
        '区块链': 'Blockchain',
        '虚拟现实': 'Virtual_Reality',
        '增强现实': 'Augmented_Reality',
        '工业互联网': 'Industrial_Internet',
        '智能制造': 'Smart_Manufacturing',
        '新基建': 'New_Infrastructure',
        '数字货币': 'Digital_Currency',
        '网络安全': 'Cybersecurity',
        '军工': 'Military_Industry',
        '航空航天': 'Aerospace',
        '海南自贸': 'Hainan_Free_Trade',
        '雄安新区': 'Xiongan_New_Area',
        '粤港澳大湾区': 'Guangdong_Hong_Kong_Macao_Greater_Bay_Area',
        '长三角一体化': 'Yangtze_River_Delta_Integration',
        '京津冀一体化': 'Beijing_Tianjin_Hebei_Integration',
        '一带一路': 'Belt_and_Road',
        '乡村振兴': 'Rural_Revitalization',
        '共同富裕': 'Common_Prosperity',
        '碳中和': 'Carbon_Neutrality',
        '数字经济': 'Digital_Economy',
        '元宇宙': 'Metaverse',
        'AIGC': 'AIGC',
        'ChatGPT': 'ChatGPT',
        '东数西算': 'East_Data_West_Computing',
        '专精特新': 'Specialized_Refined_Distinctive_Innovative',
        '北交所': 'Beijing_Stock_Exchange',
        '创业板': 'ChiNext',
        '科创板': 'STAR_Market',
        '注册制': 'Registration_System',
        '退市整理': 'Delisting_Arrangement',
        'ST股票': 'ST_Stocks',
        '次新股': 'Sub_New_Stocks',
        '高送转': 'High_Transfer',
        '分拆上市': 'Spin_off_Listing',
        '并购重组': 'Merger_Acquisition_Restructuring',
        '股权转让': 'Equity_Transfer',
        '定增': 'Private_Placement',
        '回购': 'Share_Buyback',
        '减持': 'Share_Reduction',
        '举牌': 'Placard_Raising',
        '破净股': 'Stocks_Below_Net_Value',
        '低价股': 'Low_Price_Stocks',
        '高价股': 'High_Price_Stocks',
        '超跌': 'Oversold',
        '强势股': 'Strong_Stocks',
        '题材股': 'Theme_Stocks',
        '概念股': 'Concept_Stocks',
        '蓝筹股': 'Blue_Chip_Stocks',
        '白马股': 'White_Horse_Stocks',
        '成长股': 'Growth_Stocks',
        '价值股': 'Value_Stocks',
        '周期股': 'Cyclical_Stocks',
        '防御股': 'Defensive_Stocks',
        '消费股': 'Consumer_Stocks',
        '科技股': 'Technology_Stocks',
        '金融股': 'Financial_Stocks',
        '地产股': 'Real_Estate_Stocks',
        '资源股': 'Resource_Stocks',
        '医药股': 'Pharmaceutical_Stocks',
        '新能源股': 'New_Energy_Stocks',
        '军工股': 'Military_Stocks',
        '基建股': 'Infrastructure_Stocks',
        '农业股': 'Agriculture_Stocks',
        '环保股': 'Environmental_Protection_Stocks',
        '教育股': 'Education_Stocks',
        '传媒股': 'Media_Stocks',
        '游戏股': 'Gaming_Stocks',
        '旅游股': 'Tourism_Stocks',
        '酒类股': 'Liquor_Stocks',
        '食品股': 'Food_Stocks',
        '家电股': 'Home_Appliance_Stocks',
        '汽车股': 'Automobile_Stocks',
        '钢铁股': 'Steel_Stocks',
        '煤炭股': 'Coal_Stocks',
        '有色股': 'Non_Ferrous_Stocks',
        '化工股': 'Chemical_Stocks',
        '建材股': 'Building_Material_Stocks',
        '机械股': 'Machinery_Stocks',
        '电力股': 'Electric_Power_Stocks',
        '通信股': 'Communication_Stocks',
        '电子股': 'Electronics_Stocks',
        '计算机股': 'Computer_Stocks',
        '互联网股': 'Internet_Stocks',
        '电商股': 'E_commerce_Stocks',
        '物流股': 'Logistics_Stocks',
        '航运股': 'Shipping_Stocks',
        '航空股': 'Aviation_Stocks',
        '铁路股': 'Railway_Stocks',
        '公路股': 'Highway_Stocks',
        '港口股': 'Port_Stocks',
        '机场股': 'Airport_Stocks',
        '银行股': 'Banking_Stocks',
        '保险股': 'Insurance_Stocks',
        '券商股': 'Securities_Stocks',
        '信托股': 'Trust_Stocks',
        '租赁股': 'Leasing_Stocks',
        '担保股': 'Guarantee_Stocks',
        '小贷股': 'Microfinance_Stocks',
        '支付股': 'Payment_Stocks',
        '征信股': 'Credit_Investigation_Stocks',
        '区域银行': 'Regional_Banks',
        '城商行': 'City_Commercial_Banks',
        '农商行': 'Rural_Commercial_Banks',
        '村镇银行': 'Village_Banks',
        '民营银行': 'Private_Banks',
        '外资银行': 'Foreign_Banks',
        '政策银行': 'Policy_Banks',
        '开发银行': 'Development_Banks',
        '进出口银行': 'Import_Export_Banks',
        '农业发展银行': 'Agricultural_Development_Banks'
    }

    # 如果找到映射，返回英文名
    if chinese_name in sector_mapping:
        return sector_mapping[chinese_name]

    # 如果没有找到映射，使用简化处理
    # 移除特殊字符，用下划线替换空格
    import re
    english_name = re.sub(r'[^\w\s]', '', chinese_name)
    english_name = re.sub(r'\s+', '_', english_name.strip())

    # 如果还是中文，使用拼音库转换（如果可用）
    try:
        from pypinyin import lazy_pinyin, Style
        pinyin_list = lazy_pinyin(chinese_name, style=Style.NORMAL)
        english_name = '_'.join([word.capitalize() for word in pinyin_list])
    except ImportError:
        # 如果没有pypinyin库，使用简化的处理
        english_name = f"Sector_{hash(chinese_name) % 10000}"

    return english_name

def get_tpdog_sector_list(sector_type, token):
    """
    获取TPDOG版块列表
    
    Args:
        sector_type: 版块类型 ('bki': 行业版块, 'bkc': 概念版块, 'bkr': 地域版块)
        token: TPDOG API token
    
    Returns:
        DataFrame: 版块列表数据，失败返回None
    """
    try:
        url = f"https://www.tpdog.com/api/bk/list?type={sector_type}&token={token}"
        
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 1000 and data.get('content'):
                df = pd.DataFrame(data['content'])
                print(f"✅ 成功获取{sector_type}版块列表: {len(df)} 个版块")
                logging.info(f"成功获取{sector_type}版块列表: {len(df)} 个版块")
                return df
            else:
                raise ValueError(f"TPDOG版块列表API返回错误: {data}")
        else:
            raise ValueError(f"TPDOG版块列表API请求失败，状态码: {response.status_code}")
            
    except Exception as e:
        logging.error(f"获取{sector_type}版块列表失败: {e}")
        print(f"❌ 获取{sector_type}版块列表失败: {e}")
        return None

def get_tpdog_sector_funds(sector_type, token, field='m_net', sort=2):
    """
    获取TPDOG版块资金流数据
    
    Args:
        sector_type: 版块类型 ('bki': 行业版块, 'bkc': 概念版块, 'bkr': 地域版块)
        token: TPDOG API token
        field: 排序字段，默认按主力净流入排序
        sort: 排序方式，2为倒序（从大到小）
    
    Returns:
        DataFrame: 版块资金流数据，失败返回None
    """
    try:
        if not is_trading_time():
            print(f"⚠️ 非交易时间，跳过{sector_type}版块资金流获取")
            return None
        
        url = f"https://www.tpdog.com/api/hs/current/bk_funds?bk_type={sector_type}&field={field}&sort={sort}&token={token}"
        
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 1000 and data.get('content'):
                df = pd.DataFrame(data['content'])
                print(f"✅ 成功获取{sector_type}版块资金流数据: {len(df)} 条")
                logging.info(f"成功获取{sector_type}版块资金流数据: {len(df)} 条")
                return df
            else:
                raise ValueError(f"TPDOG版块资金流API返回错误: {data}")
        else:
            raise ValueError(f"TPDOG版块资金流API请求失败，状态码: {response.status_code}")
            
    except Exception as e:
        logging.error(f"获取{sector_type}版块资金流失败: {e}")
        print(f"❌ 获取{sector_type}版块资金流失败: {e}")
        return None

def get_tpdog_sector_stocks(sector_code, token):
    """
    获取TPDOG版块内个股列表

    Args:
        sector_code: 版块代码，如 'bki.880158'
        token: TPDOG API token

    Returns:
        DataFrame: 个股列表数据，失败返回None
    """
    try:
        url = f"https://www.tpdog.com/api/hs/stocks/list_board?code={sector_code}&token={token}"

        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 1000 and data.get('content'):
                df = pd.DataFrame(data['content'])
                print(f"✅ 成功获取版块{sector_code}个股列表: {len(df)} 只股票")
                logging.info(f"成功获取版块{sector_code}个股列表: {len(df)} 只股票")
                return df
            else:
                raise ValueError(f"TPDOG版块个股API返回错误: {data}")
        else:
            raise ValueError(f"TPDOG版块个股API请求失败，状态码: {response.status_code}")

    except Exception as e:
        logging.error(f"获取版块{sector_code}个股列表失败: {e}")
        print(f"❌ 获取版块{sector_code}个股列表失败: {e}")
        return None

def get_tpdog_sector_stocks_funds(sector_code, token):
    """
    获取TPDOG版块内个股的资金流数据

    Args:
        sector_code: 版块代码，如 'bki.880158'
        token: TPDOG API token

    Returns:
        DataFrame: 个股资金流数据，失败返回None
    """
    try:
        # 首先获取版块内的个股列表
        stocks_df = get_tpdog_sector_stocks(sector_code, token)
        if stocks_df is None or stocks_df.empty:
            return None

        # 获取所有个股的资金流数据
        all_stocks_funds = []

        # 分别获取上海和深圳的股票资金流数据
        for exchange in ['zssh', 'zssz']:
            try:
                if not is_trading_time():
                    # 非交易时间使用测试参数
                    url = f"https://www.tpdog.com/api/hs/current/scans?zs_type={exchange}&sort=2&field=income&filter=&t=1&token={token}"
                else:
                    url = f"https://www.tpdog.com/api/hs/current/scans?zs_type={exchange}&sort=2&field=income&filter=&token={token}"

                response = requests.get(url, timeout=15)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('code') == 1000 and data.get('content'):
                        exchange_stocks = pd.DataFrame(data['content'])
                        all_stocks_funds.append(exchange_stocks)
                        print(f"    ✅ 获取{exchange}股票资金流数据: {len(exchange_stocks)} 只股票")
                    elif data.get('code') == 1002:
                        # 示例数据
                        exchange_stocks = pd.DataFrame(data.get('content', []))
                        if not exchange_stocks.empty:
                            all_stocks_funds.append(exchange_stocks)
                            print(f"    ⚠️ 获取{exchange}示例资金流数据: {len(exchange_stocks)} 只股票")

                # 避免请求过于频繁
                time_module.sleep(0.3)

            except Exception as e:
                print(f"    ❌ 获取{exchange}股票资金流失败: {e}")
                continue

        if not all_stocks_funds:
            print(f"    ❌ 未能获取任何股票资金流数据")
            return None

        # 合并所有交易所的数据
        all_funds_df = pd.concat(all_stocks_funds, ignore_index=True)

        # 筛选出版块内的个股
        sector_stocks_codes = set(stocks_df['code'].astype(str))
        sector_funds_df = all_funds_df[all_funds_df['code'].astype(str).isin(sector_stocks_codes)]

        if sector_funds_df.empty:
            print(f"    ⚠️ 版块{sector_code}内未找到匹配的个股资金流数据")
            return None

        print(f"    ✅ 成功获取版块{sector_code}内{len(sector_funds_df)}只个股的资金流数据")
        logging.info(f"成功获取版块{sector_code}内{len(sector_funds_df)}只个股的资金流数据")
        return sector_funds_df

    except Exception as e:
        logging.error(f"获取版块{sector_code}个股资金流失败: {e}")
        print(f"❌ 获取版块{sector_code}个股资金流失败: {e}")
        return None

def get_tpdog_sector_quote(sector_type, sector_code, token):
    """
    获取TPDOG版块实时行情数据（使用版块筛选接口）

    Args:
        sector_type: 版块类型 ('bki': 行业版块, 'bkc': 概念版块, 'bkr': 地域版块)
        sector_code: 版块代码，如 '880158'
        token: TPDOG API token

    Returns:
        dict: 版块行情数据，失败返回None
    """
    try:
        # 使用版块筛选接口获取特定版块的行情数据
        if not is_trading_time():
            # 非交易时间使用测试参数
            url = f"https://www.tpdog.com/api/current/bk_scans?bk_type={sector_type}&sort=1&field=code&filter=code={sector_code}&t=1&token={token}"
        else:
            url = f"https://www.tpdog.com/api/current/bk_scans?bk_type={sector_type}&sort=1&field=code&filter=code={sector_code}&token={token}"

        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 1000 and data.get('content'):
                # 返回匹配的版块数据（应该只有一个）
                content = data['content']
                if content and len(content) > 0:
                    return content[0]  # 返回第一个匹配的版块
                else:
                    print(f"⚠️ 版块{sector_type}.{sector_code}未找到行情数据")
                    return None
            elif data.get('code') == 1002:
                # 示例数据，非交易时间返回的测试数据
                print(f"⚠️ 版块{sector_type}.{sector_code}返回示例数据（非交易时间）")
                content = data.get('content', [])
                return content[0] if content else None
            else:
                raise ValueError(f"TPDOG版块行情API返回错误: {data}")
        else:
            raise ValueError(f"TPDOG版块行情API请求失败，状态码: {response.status_code}")

    except Exception as e:
        logging.error(f"获取版块{sector_type}.{sector_code}行情失败: {e}")
        print(f"❌ 获取版块{sector_type}.{sector_code}行情失败: {e}")
        return None

def check_and_update_sector_lists():
    """
    检查并更新版块列表（收盘时执行）
    如果版块列表文件不存在，则获取并保存
    """
    try:
        date_folder = get_date_folder()
        timestamp = get_timestamp()

        # 检查行业版块列表
        industry_list_file = os.path.join(date_folder, 'tpdog_industry_list.json')
        if not os.path.exists(industry_list_file):
            print("🔄 获取行业版块列表...")
            industry_df = get_tpdog_sector_list('bki', TPDOG_TOKEN)
            if industry_df is not None:
                # 保存JSON缓存文件（用于判断是否需要重新获取）
                industry_df.to_json(industry_list_file, orient='records', indent=2)
                print(f"✅ 行业版块列表已保存: {len(industry_df)} 个版块")
                logging.info(f"行业版块列表已保存到: {industry_list_file}")
        else:
            print("✅ 行业版块列表已存在，跳过获取")

        # 检查概念版块列表
        concept_list_file = os.path.join(date_folder, 'tpdog_concept_list.json')
        if not os.path.exists(concept_list_file):
            print("🔄 获取概念版块列表...")
            concept_df = get_tpdog_sector_list('bkc', TPDOG_TOKEN)
            if concept_df is not None:
                # 保存JSON缓存文件（用于判断是否需要重新获取）
                concept_df.to_json(concept_list_file, orient='records', indent=2)
                print(f"✅ 概念版块列表已保存: {len(concept_df)} 个版块")
                logging.info(f"概念版块列表已保存到: {concept_list_file}")
        else:
            print("✅ 概念版块列表已存在，跳过获取")

    except Exception as e:
        logging.error(f"检查更新版块列表失败: {e}")
        print(f"❌ 检查更新版块列表失败: {e}")

def get_sector_funds_data():
    """
    获取版块资金流数据（交易时间执行）
    """
    try:
        if not is_trading_time():
            print("⚠️ 非交易时间，跳过版块资金流获取")
            return

        date_folder = get_date_folder()
        timestamp = get_timestamp()

        print("\n" + "=" * 50)
        print("📊 开始获取TPDOG版块资金流数据")
        print("=" * 50)

        # 1. 获取行业版块资金流
        print("🔄 获取行业版块资金流...")
        industry_funds_df = get_tpdog_sector_funds('bki', TPDOG_TOKEN)
        if industry_funds_df is not None:
            # 保存完整数据
            industry_funds_file = os.path.join(date_folder, f'tpdog_industry_funds_{timestamp}.csv')
            industry_funds_df.to_csv(industry_funds_file, index=False, encoding='utf-8-sig')

            # 打印前10名
            print("\n📈 行业版块资金流前10名:")
            top_10_industry = industry_funds_df.head(10)
            for idx, row in top_10_industry.iterrows():
                print(f"  {idx+1:2d}. {row['name']:15s} | 主力净流入: {format_amount(row['m_net']):>10s} | 净流入比例: {row.get('m_in_ratio', 0):6.2f}%")

            logging.info(f"行业版块资金流数据已保存: {industry_funds_file}")

        # 2. 获取概念版块资金流
        print("\n🔄 获取概念版块资金流...")
        concept_funds_df = get_tpdog_sector_funds('bkc', TPDOG_TOKEN)
        if concept_funds_df is not None:
            # 保存完整数据
            concept_funds_file = os.path.join(date_folder, f'tpdog_concept_funds_{timestamp}.csv')
            concept_funds_df.to_csv(concept_funds_file, index=False, encoding='utf-8-sig')

            # 打印前10名
            print("\n📈 概念版块资金流前10名:")
            top_10_concept = concept_funds_df.head(10)
            for idx, row in top_10_concept.iterrows():
                print(f"  {idx+1:2d}. {row['name']:15s} | 主力净流入: {format_amount(row['m_net']):>10s} | 净流入比例: {row.get('m_in_ratio', 0):6.2f}%")

            logging.info(f"概念版块资金流数据已保存: {concept_funds_file}")

        # 3. 获取前10版块的个股数据
        get_top_sectors_stocks_data(industry_funds_df, concept_funds_df)

        # 4. 获取前10版块的行情数据
        get_top_sectors_quotes_data(industry_funds_df, concept_funds_df)

        print("=" * 50)
        print("✅ TPDOG版块数据获取完成")
        print("=" * 50)

    except Exception as e:
        logging.error(f"获取版块资金流数据失败: {e}")
        print(f"❌ 获取版块资金流数据失败: {e}")

def get_top_sectors_stocks_data(industry_df, concept_df):
    """
    获取前10版块的个股资金流数据
    """
    try:
        import re
        date_folder = get_date_folder()
        timestamp = get_timestamp()

        print("\n🔄 获取前10版块的个股资金流数据...")

        # 处理行业版块前10
        if industry_df is not None and not industry_df.empty:
            top_10_industry = industry_df.head(10)
            for idx, row in top_10_industry.iterrows():
                sector_code = f"bki.{row['code']}"
                sector_name = row['name']

                print(f"  💰 获取行业版块 '{sector_name}' 的个股资金流...")
                stocks_funds_df = get_tpdog_sector_stocks_funds(sector_code, TPDOG_TOKEN)

                if stocks_funds_df is not None and not stocks_funds_df.empty:
                    # 按主力净流入排序
                    stocks_funds_df = stocks_funds_df.sort_values('income', ascending=False)

                    # 生成英文文件名
                    safe_name = re.sub(r'[\\/*?:"<>|]', "", sector_name)
                    english_name = get_english_sector_name(sector_name)
                    stocks_file = os.path.join(date_folder, f'{english_name}_Industry_Stocks_Funds_{timestamp}.csv')
                    stocks_funds_df.to_csv(stocks_file, index=False, encoding='utf-8-sig')

                    # 打印前5名个股资金流
                    print(f"    📈 {sector_name} 个股资金流前5名:")
                    top_5_stocks = stocks_funds_df.head(5)
                    for i, stock_row in top_5_stocks.iterrows():
                        print(f"      {i+1}. {stock_row['name']:10s} | 主力流入: {format_amount(stock_row['income']):>10s}")

                    print(f"    ✅ 已保存 {len(stocks_funds_df)} 只个股资金流数据到: {stocks_file}")
                    logging.info(f"行业版块'{sector_name}'个股资金流数据已保存: {stocks_file}")

                # 避免请求过于频繁
                time_module.sleep(0.5)

        # 处理概念版块前10
        if concept_df is not None and not concept_df.empty:
            top_10_concept = concept_df.head(10)
            for idx, row in top_10_concept.iterrows():
                sector_code = f"bkc.{row['code']}"
                sector_name = row['name']

                print(f"  💰 获取概念版块 '{sector_name}' 的个股资金流...")
                stocks_funds_df = get_tpdog_sector_stocks_funds(sector_code, TPDOG_TOKEN)

                if stocks_funds_df is not None and not stocks_funds_df.empty:
                    # 按主力净流入排序
                    stocks_funds_df = stocks_funds_df.sort_values('income', ascending=False)

                    # 生成英文文件名
                    safe_name = re.sub(r'[\\/*?:"<>|]', "", sector_name)
                    english_name = get_english_sector_name(sector_name)
                    stocks_file = os.path.join(date_folder, f'{english_name}_Concept_Stocks_Funds_{timestamp}.csv')
                    stocks_funds_df.to_csv(stocks_file, index=False, encoding='utf-8-sig')

                    # 打印前5名个股资金流
                    print(f"    📈 {sector_name} 个股资金流前5名:")
                    top_5_stocks = stocks_funds_df.head(5)
                    for i, stock_row in top_5_stocks.iterrows():
                        print(f"      {i+1}. {stock_row['name']:10s} | 主力流入: {format_amount(stock_row['income']):>10s}")

                    print(f"    ✅ 已保存 {len(stocks_funds_df)} 只个股资金流数据到: {stocks_file}")
                    logging.info(f"概念版块'{sector_name}'个股资金流数据已保存: {stocks_file}")

                # 避免请求过于频繁
                time_module.sleep(0.5)

    except Exception as e:
        logging.error(f"获取前10版块个股资金流数据失败: {e}")
        print(f"❌ 获取前10版块个股资金流数据失败: {e}")

def get_top_sectors_quotes_data(industry_df, concept_df):
    """
    获取前10版块的行情数据
    """
    try:
        date_folder = get_date_folder()
        timestamp = get_timestamp()

        print("\n🔄 获取前10版块的行情数据...")

        all_quotes = []

        # 处理行业版块前10
        if industry_df is not None and not industry_df.empty:
            top_10_industry = industry_df.head(10)
            for idx, row in top_10_industry.iterrows():
                sector_type = 'bki'
                sector_code = row['code']
                sector_name = row['name']

                quote_data = get_tpdog_sector_quote(sector_type, sector_code, TPDOG_TOKEN)
                if quote_data:
                    quote_data['sector_type'] = '行业'
                    quote_data['sector_name'] = sector_name
                    quote_data['sector_code'] = f"{sector_type}.{sector_code}"
                    all_quotes.append(quote_data)

                # 避免请求过于频繁
                time_module.sleep(0.3)

        # 处理概念版块前10
        if concept_df is not None and not concept_df.empty:
            top_10_concept = concept_df.head(10)
            for idx, row in top_10_concept.iterrows():
                sector_type = 'bkc'
                sector_code = row['code']
                sector_name = row['name']

                quote_data = get_tpdog_sector_quote(sector_type, sector_code, TPDOG_TOKEN)
                if quote_data:
                    quote_data['sector_type'] = '概念'
                    quote_data['sector_name'] = sector_name
                    quote_data['sector_code'] = f"{sector_type}.{sector_code}"
                    all_quotes.append(quote_data)

                # 避免请求过于频繁
                time_module.sleep(0.3)

        # 保存所有行情数据
        if all_quotes:
            quotes_df = pd.DataFrame(all_quotes)
            quotes_file = os.path.join(date_folder, f'tpdog_sectors_quotes_{timestamp}.csv')
            quotes_df.to_csv(quotes_file, index=False, encoding='utf-8-sig')

            # 打印涨幅前10的版块
            if 'rise_rate' in quotes_df.columns:
                quotes_df['rise_rate'] = pd.to_numeric(quotes_df['rise_rate'], errors='coerce')
                top_gainers = quotes_df.nlargest(10, 'rise_rate')

                print("\n📈 涨幅前10的版块:")
                for idx, row in top_gainers.iterrows():
                    print(f"  {idx+1:2d}. {row['sector_name']:15s} ({row['sector_type']}) | 涨跌幅: {row['rise_rate']:6.2f}% | 最新价: {row.get('price', 0):8.2f}")

            print(f"✅ 版块行情数据已保存: {quotes_file} ({len(all_quotes)} 个版块)")
            logging.info(f"版块行情数据已保存: {quotes_file}")

    except Exception as e:
        logging.error(f"获取前10版块行情数据失败: {e}")
        print(f"❌ 获取前10版块行情数据失败: {e}")

def run_threaded(job_func):
    """在后台线程中运行任务"""
    job_thread = threading.Thread(target=job_func)
    job_thread.daemon = True
    job_thread.start()

def schedule_jobs():
    """设置定时任务"""
    # 任务1: 收盘后检查版块列表（每日15:10执行）
    schedule.every().day.at("15:10").do(run_threaded, check_and_update_sector_lists)

    # 任务2: 交易时间每2分钟获取版块资金流数据
    schedule.every(2).minutes.do(run_threaded, get_sector_funds_data)

    print("✅ 定时任务已设置:")
    print("   - 每日15:10检查更新版块列表")
    print("   - 每2分钟获取版块资金流数据（仅交易时间）")
    logging.info("定时任务已设置: 每日15:10检查版块列表，每2分钟获取资金流数据")

def main():
    """主程序"""
    print("=" * 60)
    print("🚀 TPDOG版块资金流监控程序启动")
    print("=" * 60)
    print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"交易时间: 09:30-11:30 / 13:00-15:00")
    print(f"数据获取频率: 每2分钟")
    print("=" * 60)

    # 设置日志
    setup_logging()

    # 首次启动时立即执行检查
    print("\n🔄 首次启动，立即执行版块列表检查...")
    check_and_update_sector_lists()

    # 如果是交易时间，立即执行一次数据获取
    if is_trading_time():
        print("\n🔄 当前为交易时间，立即执行一次数据获取...")
        get_sector_funds_data()
    else:
        print("\n⏰ 当前非交易时间，等待交易时间开始...")

    # 设置定时任务
    schedule_jobs()

    # 主循环
    print("\n🔄 进入监控循环，按Ctrl+C退出...")
    try:
        while True:
            schedule.run_pending()
            time_module.sleep(30)  # 每30秒检查一次定时任务
    except KeyboardInterrupt:
        print("\n\n👋 程序已停止")
        logging.info("程序手动停止")

if __name__ == "__main__":
    main()
