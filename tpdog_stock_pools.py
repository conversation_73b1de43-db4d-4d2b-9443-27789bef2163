
import os
import requests
import json
import time
from datetime import datetime, time as dt_time, timedelta
from dotenv import load_dotenv
import pandas as pd


def save_summary_to_file(rankings_data, filepath):
    """
    保存汇总统计到文本文件
    """
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"TPDog 股票池数据汇总统计\n")
            f.write("=" * 50 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"强制更新: {'开启' if FORCE_UPDATE else '关闭'}\n")
            f.write(f"收盘时间后: {'是' if is_after_market_close() else '否'}\n\n")

            total_stocks = 0
            for pool_type, pool_info in rankings_data.items():
                pool_name = pool_info['name']
                df = pool_info['dataframe']
                count = len(df)
                total_stocks += count

                f.write(f"{pool_name}: {count} 只股票\n")

                if count > 0:
                    # 各个池子的特色统计
                    if pool_type == 'limitup':
                        # 涨停池统计
                        avg_c_times = df['连板数'].mean() if '连板数' in df.columns else 0
                        max_c_times = df['连板数'].max() if '连板数' in df.columns else 0
                        f.write(f"  平均连板数: {avg_c_times:.2f}\n")
                        f.write(f"  最高连板数: {max_c_times}\n")

                        # 按连板数统计
                        if '连板数' in df.columns:
                            c_times_counts = df['连板数'].value_counts().sort_index()
                            f.write(f"  连板数分布: ")
                            for c_times, count in c_times_counts.items():
                                f.write(f"{c_times}板{count}只 ")
                            f.write("\n")

                    elif pool_type == 'limitdown':
                        # 跌停池统计
                        avg_pe = df['市盈率'].mean() if '市盈率' in df.columns and df['市盈率'].notna().any() else 0
                        f.write(f"  平均市盈率: {avg_pe:.2f}\n")

                    elif pool_type == 'fire':
                        # 炸板池统计
                        avg_f_times = df['炸板次数'].mean() if '炸板次数' in df.columns else 0
                        max_f_times = df['炸板次数'].max() if '炸板次数' in df.columns else 0
                        f.write(f"  平均炸板次数: {avg_f_times:.2f}\n")
                        f.write(f"  最高炸板次数: {max_f_times}\n")

                    # 通用统计
                    avg_rise_rate = df['涨跌幅'].mean()
                    avg_amt = df['成交额'].mean()
                    f.write(f"  平均涨跌幅: {avg_rise_rate:.2f}%\n")
                    f.write(f"  平均成交额: {avg_amt:,.0f} 元\n")

                f.write("\n")

            f.write(f"总计股票数量: {total_stocks} 只\n\n")

            # 添加各池子前5名
            for pool_type, pool_info in rankings_data.items():
                pool_name = pool_info['name']
                df = pool_info['dataframe']

                if len(df) == 0:
                    continue

                f.write(f"{pool_name}前5名:\n")
                f.write("-" * 30 + "\n")

                if pool_type == 'limitup':
                    top5 = df.nlargest(5, '连板数')
                    for idx, (_, row) in enumerate(top5.iterrows(), 1):
                        f.write(f"{idx}. {row['股票名称']} ({row['股票代码']}) - {row['连板数']}板\n")
                elif pool_type == 'limitdown':
                    top5 = df.nsmallest(5, '涨跌幅')
                    for idx, (_, row) in enumerate(top5.iterrows(), 1):
                        f.write(f"{idx}. {row['股票名称']} ({row['股票代码']}) - {row['涨跌幅']:.2f}%\n")
                else:  # fire
                    top5 = df.nlargest(5, '炸板次数')
                    for idx, (_, row) in enumerate(top5.iterrows(), 1):
                        f.write(f"{idx}. {row['股票名称']} ({row['股票代码']}) - {row['炸板次数']}次炸板\n")

                f.write("\n")

    except Exception as e:
        print(f"❌ 保存汇总统计失败: {e}")


def print_fetch_records():
    """
    打印获取记录统计
    """
    records = load_fetch_records()

    if not records:
        print("📝 暂无获取记录")
        return

    print("\n" + "=" * 60)
    print("📝 今日获取记录统计")
    print("=" * 60)

    success_count = 0
    failed_count = 0
    skipped_count = 0

    for pool_type, record in records.items():
        status = record.get('status', 'unknown')
        pool_name = record.get('pool_name', pool_type)
        stock_count = record.get('stock_count', 0)
        last_time = record.get('last_fetch_time', 'N/A')
        message = record.get('message', 'N/A')

        status_icon = {'success': '✅', 'failed': '❌', 'skipped': '⏭️'}.get(status, '❓')

        if status == 'success':
            success_count += 1
        elif status == 'failed':
            failed_count += 1
        elif status == 'skipped':
            skipped_count += 1

        print(f"{status_icon} {pool_name:<12} {status:<8} {stock_count:<6}只 {last_time:<20} {message}")

    print("-" * 60)
    print(f"✅ 成功获取: {success_count} 个股票池")
    print(f"❌ 获取失败: {failed_count} 个股票池")
    print(f"⏭️ 跳过股票池: {skipped_count} 个股票池")
    print(f"📊 总计记录: {len(records)} 个股票池")


def clear_cache():
    """
    清理缓存和记录数据
    """
    try:
        date_dir = get_date_folder()

        # 清理记录文件
        records_file = os.path.join(date_dir, RECORD_FILE_NAME)
        if os.path.exists(records_file):
            os.remove(records_file)
            print(f"🗑️ 获取记录文件已清理: {records_file}")
        else:
            print("📝 无获取记录文件需要清理")

        # 清理缓存文件
        cache_file = os.path.join(date_dir, POOL_CACHE_FILE_NAME)
        if os.path.exists(cache_file):
            os.remove(cache_file)
            print(f"🗑️ 缓存文件已清理: {cache_file}")
        else:
            print("📝 无缓存文件需要清理")

    except Exception as e:
        print(f"❌ 清理文件失败: {e}")


def print_cache_info():
    """
    显示缓存和记录信息
    """
    print("\n" + "=" * 60)
    print("🗄️ 股票池数据缓存和记录信息")
    print("=" * 60)

    today = datetime.now().strftime('%Y-%m-%d')
    print(f"📅 当前日期: {today}")
    print(f"🔧 强制更新: {'开启' if FORCE_UPDATE else '关闭'}")
    print(f"📈 是否为收盘后: {'是' if is_after_market_close() else '否'}")

    # 显示获取记录
    print_fetch_records()

    # 检查本地文件
    date_dir = get_date_folder()
    if os.path.exists(date_dir):
        files = os.listdir(date_dir)
        print(f"\n📁 本地文件 ({len(files)} 个):")
        for file in sorted(files):
            file_path = os.path.join(date_dir, file)
            file_size = os.path.getsize(file_path) / 1024  # KB
            mod_time = datetime.fromtimestamp(os.path.getmtime(file_path)).strftime('%H:%M:%S')
            print(f"  📄 {file:<40} {file_size:>6.1f}KB {mod_time}")
    else:
        print(f"\n📁 本地目录不存在: {date_dir}")
        import os


import requests
import json
import time
from datetime import datetime
from dotenv import load_dotenv
import pandas as pd

# 配置项定义
FORCE_UPDATE = False  # 强制更新开关，默认为False（设为True时即使本地已有文件也会全部更新）
MARKET_CLOSE_TIME = "15:00"  # 收盘时间定义
RECORD_FILE_NAME = "fetch_records.json"  # 记录文件名
POOL_CACHE_FILE_NAME = "pool_cache.json"  # 股票池缓存文件名


def load_tpdog_token():
    """
    加载TPDog Token从环境变量
    """
    # 加载 .env 文件中的环境变量
    load_dotenv()

    # 获取TPDOG_TOKEN
    token = os.getenv("TPDOG_TOKEN")

    if not token:
        print("❌ 错误: 未在 .env 文件中找到 TPDOG_TOKEN")
        print("   请在项目根目录创建 .env 文件，并添加: TPDOG_TOKEN=你的token")
        return None

    print(f"✅ 成功加载TPDog Token: {token[:10]}...")
    return token


def is_trading_day(token, date=None):
    """
    检查指定日期是否为交易日

    参数:
    - token: TPDog API Token
    - date: 日期，格式 yyyy-MM-dd，默认为当前日期

    返回:
    - True: 是交易日
    - False: 不是交易日
    - None: 检查失败
    """
    if date is None:
        date = datetime.now().strftime('%Y-%m-%d')

    api_url = "https://www.tpdog.com/api/hs/trading_day/is"

    params = {
        'date': date,
        'token': token
    }

    try:
        response = requests.get(api_url, params=params, timeout=10)
        response.raise_for_status()

        data = response.json()

        if data.get("code") == 1000:
            is_trading = data.get("content", {}).get("is_trainding", False)
            print(f"📅 {date} {'是' if is_trading else '不是'}交易日")
            return is_trading
        else:
            print(f"❌ 检查交易日失败: {data.get('message', '未知错误')}")
            return None

    except Exception as e:
        print(f"❌ 检查交易日失败: {e}")
        return None


def get_previous_trading_day(token, date=None, max_attempts=10):
    """
    获取指定日期的上一个交易日

    参数:
    - token: TPDog API Token
    - date: 日期，格式 yyyy-MM-dd，默认为当前日期
    - max_attempts: 最大尝试次数，防止无限循环

    返回:
    - 上一个交易日的日期字符串
    - None: 查找失败
    """
    if date is None:
        date = datetime.now().strftime('%Y-%m-%d')

    current_date = datetime.strptime(date, '%Y-%m-%d')
    attempts = 0

    print(f"🔍 正在查找 {date} 的上一个交易日...")

    while attempts < max_attempts:
        # 往前推一天
        current_date = current_date - timedelta(days=1)
        check_date = current_date.strftime('%Y-%m-%d')

        # 检查是否为交易日
        is_trading = is_trading_day(token, check_date)

        if is_trading is None:
            print(f"⚠️ 无法检查 {check_date} 是否为交易日，跳过")
            attempts += 1
            continue

        if is_trading:
            print(f"✅ 找到上一个交易日: {check_date}")
            return check_date

        attempts += 1
        time.sleep(0.1)  # 控制请求频率

    print(f"❌ 在 {max_attempts} 次尝试内未找到交易日")
    return None


def get_valid_trading_date(token, date=None):
    """
    获取有效的交易日期
    如果指定日期不是交易日，则返回上一个交易日

    参数:
    - token: TPDog API Token
    - date: 日期，格式 yyyy-MM-dd，默认为当前日期

    返回:
    - 有效的交易日期字符串
    - None: 获取失败
    """
    if date is None:
        date = datetime.now().strftime('%Y-%m-%d')

    print(f"🔍 检查日期 {date} 是否为交易日...")

    # 首先检查指定日期是否为交易日
    is_trading = is_trading_day(token, date)

    if is_trading is None:
        print(f"❌ 无法检查 {date} 是否为交易日")
        return None

    if is_trading:
        print(f"✅ {date} 是交易日，直接使用")
        return date

    # 如果不是交易日，查找上一个交易日
    print(f"⚠️ {date} 不是交易日，查找上一个交易日...")
    previous_trading_day = get_previous_trading_day(token, date)

    if previous_trading_day:
        print(f"✅ 将使用交易日: {previous_trading_day}")
        return previous_trading_day
    else:
        print(f"❌ 无法找到有效的交易日")
        return None


def is_after_market_close():
    """
    判断当前时间是否为收盘后（15:00后）
    """
    now = datetime.now()
    market_close = datetime.strptime(MARKET_CLOSE_TIME, "%H:%M").time()
    current_time = now.time()

    return current_time > market_close


def get_date_folder():
    """
    获取当前日期文件夹路径
    """
    date_folder = datetime.now().strftime('%Y-%m-%d')
    return os.path.join("涨停池", date_folder)


def load_fetch_records():
    """
    加载获取记录文件
    """
    records_file = os.path.join(get_date_folder(), RECORD_FILE_NAME)

    if os.path.exists(records_file):
        try:
            with open(records_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠️ 警告: 读取记录文件失败: {e}")
            return {}

    return {}


def save_fetch_records(records):
    """
    保存获取记录文件
    """
    date_dir = get_date_folder()
    os.makedirs(date_dir, exist_ok=True)

    records_file = os.path.join(date_dir, RECORD_FILE_NAME)

    try:
        with open(records_file, 'w', encoding='utf-8') as f:
            json.dump(records, f, ensure_ascii=False, indent=2)
        print(f"📝 获取记录已更新: {records_file}")
    except Exception as e:
        print(f"❌ 保存记录文件失败: {e}")


def update_pool_record(records, pool_type, status, message="", stock_count=0):
    """
    更新单个股票池的记录
    """
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    if pool_type not in records:
        records[pool_type] = {}

    pool_names = {
        'limitup': '涨停池',
        'limitdown': '跌停池',
        'fire': '炸板池'
    }

    records[pool_type].update({
        'pool_name': pool_names.get(pool_type, pool_type),
        'last_fetch_time': timestamp,
        'status': status,  # 'success', 'failed', 'skipped'
        'message': message,
        'stock_count': stock_count
    })

    return records


def should_skip_pool(records, pool_type):
    """
    判断是否应该跳过该股票池的数据获取
    - 如果开启强制更新，则不跳过
    - 如果是收盘时间后且本地已有数据，则跳过
    """
    if FORCE_UPDATE:
        return False

    if not is_after_market_close():
        return False

    # 检查是否已有今日记录
    if pool_type in records:
        last_status = records[pool_type].get('status')
        if last_status == 'success':
            return True

    return False


def check_existing_pool_files(pool_type):
    """
    检查是否已有当日的股票池数据文件
    """
    date_dir = get_date_folder()

    if not os.path.exists(date_dir):
        return False

    # 检查是否有相关文件
    files = os.listdir(date_dir)

    pool_names = {
        'limitup': '涨停池',
        'limitdown': '跌停池',
        'fire': '炸板池'
    }

    pool_name = pool_names.get(pool_type, pool_type)
    pattern = f"_tpdog_{pool_name}_"

    return any(pattern in filename for filename in files)


def get_stock_pool_data(token, pool_type, date=None, auto_fallback=True):
    """
    获取股票池数据（涨停池、跌停池、炸板池）
    支持自动回退到上一个交易日

    参数:
    - token: TPDog API Token
    - pool_type: 池子类型 ('limitup', 'limitdown', 'fire')
    - date: 日期，格式 yyyy-MM-dd，默认为当前日期
    - auto_fallback: 是否自动回退到上一个交易日，默认为True
    """
    # 如果没有指定日期，获取有效的交易日期
    if date is None:
        if auto_fallback:
            date = get_valid_trading_date(token)
            if date is None:
                print(f"❌ 无法获取有效的交易日期")
                return None
        else:
            date = datetime.now().strftime('%Y-%m-%d')

    # 根据池子类型选择不同的API
    api_urls = {
        'limitup': "https://www.tpdog.com/api/hs/pool/v1/limitup/list",  # 涨停池
        'limitdown': "https://www.tpdog.com/api/hs/pool/v1/limitdown/list",  # 跌停池
        'fire': "https://www.tpdog.com/api/hs/pool/v1/fire/list"  # 炸板池
    }

    if pool_type not in api_urls:
        print(f"❌ 不支持的股票池类型: {pool_type}")
        return None

    api_url = api_urls[pool_type]

    # 设置请求参数
    params = {
        'date': date,
        'token': token
    }

    try:
        # 发送GET请求
        response = requests.get(api_url, params=params, timeout=10)
        response.raise_for_status()

        # 解析JSON响应
        data = response.json()

        # 检查API响应状态
        if data.get("code") == 1000:
            content = data.get("content", [])
            print(f"✅ 成功获取 {pool_type} 数据 ({date}): {len(content)} 只股票")
            return content
        else:
            error_message = data.get('message', '未知错误')
            print(f"❌ 获取{pool_type}数据失败 ({date}): {error_message}")

            # 如果启用自动回退且错误信息表明没有数据，尝试上一个交易日
            if auto_fallback and ("未查到相关数据" in error_message or "无数据" in error_message):
                print(f"🔄 尝试获取上一个交易日的 {pool_type} 数据...")
                previous_date = get_previous_trading_day(token, date)
                if previous_date and previous_date != date:
                    return get_stock_pool_data(token, pool_type, previous_date, auto_fallback=False)

            return None

    except Exception as e:
        print(f"❌ 获取{pool_type}数据失败 ({date}): {e}")

        # 如果启用自动回退，尝试上一个交易日
        if auto_fallback:
            print(f"🔄 尝试获取上一个交易日的 {pool_type} 数据...")
            previous_date = get_previous_trading_day(token, date)
            if previous_date and previous_date != date:
                return get_stock_pool_data(token, pool_type, previous_date, auto_fallback=False)

        return None


def get_all_pools_data(token, date=None):
    """
    获取所有股票池数据（支持增量更新和交易日检查）

    参数:
    - token: TPDog API Token
    - date: 日期
    """
    print(f"\n📊 开始获取所有股票池数据...")
    print(f"🔧 强制更新模式: {'开启' if FORCE_UPDATE else '关闭'}")
    print(f"⏰ 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📈 是否为收盘后: {'是' if is_after_market_close() else '否'}")

    # 如果没有指定日期，获取有效的交易日期
    if date is None:
        print(f"\n🔍 检查交易日...")
        date = get_valid_trading_date(token)
        if date is None:
            print(f"❌ 无法获取有效的交易日期，程序退出")
            return None
        print(f"📅 使用交易日期: {date}")
    else:
        print(f"📅 使用指定日期: {date}")

    # 加载获取记录
    records = load_fetch_records()

    pool_types = {
        'limitup': '涨停池',
        'limitdown': '跌停池',
        'fire': '炸板池'
    }

    all_pools_data = {}
    skipped_count = 0
    success_count = 0
    failed_count = 0

    for pool_type, pool_name in pool_types.items():
        # 检查是否应该跳过
        if should_skip_pool(records, pool_type):
            print(f"⏭️ 跳过 {pool_name} (已有数据)...")
            skipped_count += 1
            all_pools_data[pool_type] = {
                'name': pool_name,
                'data': [],
                'count': 0,
                'skipped': True
            }
            records = update_pool_record(records, pool_type, 'skipped', '已有数据，跳过获取')
            continue

        print(f"\n🔄 正在获取 {pool_name} 数据...")

        pool_data = get_stock_pool_data(token, pool_type, date)

        if pool_data:
            all_pools_data[pool_type] = {
                'name': pool_name,
                'data': pool_data,
                'count': len(pool_data),
                'skipped': False
            }
            success_count += 1
            records = update_pool_record(records, pool_type, 'success', '数据获取成功', len(pool_data))
        else:
            all_pools_data[pool_type] = {
                'name': pool_name,
                'data': [],
                'count': 0,
                'skipped': False
            }
            failed_count += 1
            records = update_pool_record(records, pool_type, 'failed', '数据获取失败')

        # 控制请求频率，避免触发限制
        time.sleep(0.1)  # 等待100ms

    # 保存获取记录
    save_fetch_records(records)

    print(f"\n📈 获取统计:")
    print(f"✅ 成功获取: {success_count} 个股票池")
    print(f"⏭️ 跳过股票池: {skipped_count} 个股票池")
    print(f"❌ 获取失败: {failed_count} 个股票池")

    for pool_type, pool_info in all_pools_data.items():
        if not pool_info['skipped']:
            print(f"📊 {pool_info['name']}: {pool_info['count']} 只股票")

    return all_pools_data


def create_rankings(all_pools_data):
    """
    创建各个股票池的排行榜

    参数:
    - all_pools_data: 所有股票池数据
    """
    print(f"\n{'=' * 80}")
    print("🏆 TPDog 股票池数据汇总")
    print("=" * 80)

    rankings_data = {}

    for pool_type, pool_info in all_pools_data.items():
        pool_name = pool_info['name']
        pool_data = pool_info['data']
        is_skipped = pool_info.get('skipped', False)

        if is_skipped:
            print(f"\n⏭️ {pool_name} 已跳过获取")
            continue

        if not pool_data:
            print(f"\n❌ {pool_name} 无数据")
            continue

        print(f"\n📊 {pool_name} (共 {len(pool_data)} 只股票)")
        print("-" * 60)

        # 转换为DataFrame
        df_data = []
        for stock in pool_data:
            df_data.append({
                '股票代码': stock.get('code', 'N/A'),
                '股票名称': stock.get('name', 'N/A'),
                '交易所': stock.get('type', 'N/A'),
                '收盘价': stock.get('close', 0),
                '涨跌幅': stock.get('rise_rate', 0),
                '成交额': stock.get('total_amt', 0),
                '换手率': stock.get('t_rate', 0),
                '流通市值': stock.get('cm_valuation', 0),
                '总市值': stock.get('valuation', 0),
                '行业板块': stock.get('industry', 'N/A'),
                '类型': stock.get('udtype', 'N/A'),
                '日期': stock.get('date', 'N/A'),
                # 根据不同类型添加特殊字段
                '连板数': stock.get('c_times', 0) if pool_type == 'limitup' else None,
                '封板时间': stock.get('time', 'N/A') if pool_type in ['limitup', 'fire'] else None,
                '最后封板时间': stock.get('l_time', 'N/A'),
                '封板资金': stock.get('l_amount', 0),
                '炸板次数': stock.get('f_times', 0) if pool_type in ['limitup', 'fire'] else None,
                '连板信息': stock.get('l_info', 'N/A') if pool_type in ['limitup', 'fire'] else None,
                '市盈率': stock.get('pe', 0) if pool_type == 'limitdown' else None,
                '开板次数': stock.get('o_times', 0) if pool_type == 'limitdown' else None,
                '板上成交额': stock.get('l_amt', 0) if pool_type == 'limitdown' else None,
                '涨速': stock.get('speed', 0) if pool_type == 'fire' else None,
                '振幅': stock.get('range_rate', 0) if pool_type == 'fire' else None,
                '涨停价格': stock.get('l_price', 0) if pool_type == 'fire' else None,
            })

        df = pd.DataFrame(df_data)

        # 移除值全为None的列
        df = df.dropna(axis=1, how='all')

        rankings_data[pool_type] = {
            'name': pool_name,
            'dataframe': df,
            'raw_data': pool_data
        }

        # 打印前10名
        if pool_type == 'limitup':
            # 涨停池按连板数排序
            top_stocks = df.nlargest(10, '连板数')
            print(f"{'排名':<4} {'股票名称':<12} {'连板数':<6} {'涨跌幅':<8} {'成交额(万)':<12} {'行业':<12}")
        elif pool_type == 'limitdown':
            # 跌停池按跌幅排序
            top_stocks = df.nsmallest(10, '涨跌幅')
            print(f"{'排名':<4} {'股票名称':<12} {'跌幅':<8} {'成交额(万)':<12} {'市盈率':<8} {'行业':<12}")
        else:  # fire
            # 炸板池按炸板次数排序
            top_stocks = df.nlargest(10, '炸板次数')
            print(f"{'排名':<4} {'股票名称':<12} {'炸板次数':<8} {'涨跌幅':<8} {'成交额(万)':<12} {'行业':<12}")

        print("-" * 80)

        for idx, (_, row) in enumerate(top_stocks.iterrows(), 1):
            if idx > 10:  # 只显示前10
                break

            stock_name = row['股票名称'][:10]  # 限制长度
            rise_rate = row['涨跌幅']
            total_amt = row['成交额'] / 10000 if row['成交额'] > 0 else 0  # 转换为万元
            industry = row['行业板块'][:10] if row['行业板块'] != 'N/A' else 'N/A'

            if pool_type == 'limitup':
                c_times = row['连板数']
                print(f"{idx:<4} {stock_name:<12} {c_times:<6} {rise_rate:>6.2f}% {total_amt:>10,.0f} {industry:<12}")
            elif pool_type == 'limitdown':
                pe = row['市盈率'] if row['市盈率'] and row['市盈率'] != 0 else 0
                print(f"{idx:<4} {stock_name:<12} {rise_rate:>6.2f}% {total_amt:>10,.0f} {pe:>6.2f} {industry:<12}")
            else:  # fire
                f_times = row['炸板次数']
                print(f"{idx:<4} {stock_name:<12} {f_times:<8} {rise_rate:>6.2f}% {total_amt:>10,.0f} {industry:<12}")

    return rankings_data


def save_data_to_files(rankings_data):
    """
    保存数据到涨停池文件夹，按日期分类保存
    """
    now = datetime.now()
    date_folder = now.strftime('%Y-%m-%d')
    timestamp = now.strftime('%H-%M')

    # 创建涨停池文件夹和日期子文件夹
    base_dir = "涨停池"
    date_dir = os.path.join(base_dir, date_folder)
    os.makedirs(date_dir, exist_ok=True)

    try:
        for pool_type, pool_info in rankings_data.items():
            pool_name = pool_info['name']
            df = pool_info['dataframe']
            raw_data = pool_info['raw_data']

            # 1. 保存排行榜CSV文件
            rankings_filename = f"{timestamp}_tpdog_{pool_name}_排行榜.csv"
            rankings_filepath = os.path.join(date_dir, rankings_filename)
            df.to_csv(rankings_filepath, index=False, encoding='utf-8-sig')
            print(f"💾 {pool_name}排行榜数据已保存到: {rankings_filepath}")

            # 2. 保存原始数据JSON文件
            raw_data_filename = f"{timestamp}_tpdog_{pool_name}_原始数据.json"
            raw_data_filepath = os.path.join(date_dir, raw_data_filename)
            with open(raw_data_filepath, 'w', encoding='utf-8') as f:
                json.dump({
                    'timestamp': now.strftime('%Y-%m-%d %H:%M:%S'),
                    'pool_type': pool_type,
                    'pool_name': pool_name,
                    'total_count': len(raw_data),
                    'data': raw_data
                }, f, ensure_ascii=False, indent=2)
            print(f"💾 {pool_name}原始数据已保存到: {raw_data_filepath}")

        # 3. 保存汇总统计文件
        summary_filename = f"{timestamp}_tpdog_股票池汇总统计.txt"
        summary_filepath = os.path.join(date_dir, summary_filename)
        save_summary_to_file(rankings_data, summary_filepath)
        print(f"💾 汇总统计已保存到: {summary_filepath}")

        # 4. 保存汇总JSON文件
        summary_json_filename = f"{timestamp}_tpdog_股票池汇总数据.json"
        summary_json_filepath = os.path.join(date_dir, summary_json_filename)

        summary_json_data = {}
        for pool_type, pool_info in rankings_data.items():
            summary_json_data[pool_type] = {
                'pool_name': pool_info['name'],
                'total_count': len(pool_info['raw_data']),
                'data': pool_info['raw_data']
            }

        with open(summary_json_filepath, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': now.strftime('%Y-%m-%d %H:%M:%S'),
                'date': now.strftime('%Y-%m-%d'),
                'pools': summary_json_data
            }, f, ensure_ascii=False, indent=2)
        print(f"💾 汇总JSON数据已保存到: {summary_json_filepath}")

        print(f"\n📁 所有文件已保存到目录: {date_dir}")

    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
        import traceback
        traceback.print_exc()


def save_summary_to_file(rankings_data, filepath):
    """
    保存汇总统计到文本文件
    """
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"TPDog 股票池数据汇总统计\n")
            f.write("=" * 50 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"强制更新: {'开启' if FORCE_UPDATE else '关闭'}\n\n")

            total_stocks = 0
            for pool_type, pool_info in rankings_data.items():
                pool_name = pool_info['name']
                df = pool_info['dataframe']
                count = len(df)
                total_stocks += count

                f.write(f"{pool_name}: {count} 只股票\n")

                if count > 0:
                    # 各个池子的特色统计
                    if pool_type == 'limitup':
                        # 涨停池统计
                        avg_c_times = df['连板数'].mean() if '连板数' in df.columns else 0
                        max_c_times = df['连板数'].max() if '连板数' in df.columns else 0
                        f.write(f"  平均连板数: {avg_c_times:.2f}\n")
                        f.write(f"  最高连板数: {max_c_times}\n")

                        # 按连板数统计
                        if '连板数' in df.columns:
                            c_times_counts = df['连板数'].value_counts().sort_index()
                            f.write(f"  连板数分布: ")
                            for c_times, count in c_times_counts.items():
                                f.write(f"{c_times}板{count}只 ")
                            f.write("\n")

                    elif pool_type == 'limitdown':
                        # 跌停池统计
                        avg_pe = df['市盈率'].mean() if '市盈率' in df.columns and df['市盈率'].notna().any() else 0
                        f.write(f"  平均市盈率: {avg_pe:.2f}\n")

                    elif pool_type == 'fire':
                        # 炸板池统计
                        avg_f_times = df['炸板次数'].mean() if '炸板次数' in df.columns else 0
                        max_f_times = df['炸板次数'].max() if '炸板次数' in df.columns else 0
                        f.write(f"  平均炸板次数: {avg_f_times:.2f}\n")
                        f.write(f"  最高炸板次数: {max_f_times}\n")

                    # 通用统计
                    avg_rise_rate = df['涨跌幅'].mean()
                    avg_amt = df['成交额'].mean()
                    f.write(f"  平均涨跌幅: {avg_rise_rate:.2f}%\n")
                    f.write(f"  平均成交额: {avg_amt:,.0f} 元\n")

                f.write("\n")

            f.write(f"总计股票数量: {total_stocks} 只\n\n")

            # 添加各池子前5名
            for pool_type, pool_info in rankings_data.items():
                pool_name = pool_info['name']
                df = pool_info['dataframe']

                if len(df) == 0:
                    continue

                f.write(f"{pool_name}前5名:\n")
                f.write("-" * 30 + "\n")

                if pool_type == 'limitup':
                    top5 = df.nlargest(5, '连板数')
                    for idx, (_, row) in enumerate(top5.iterrows(), 1):
                        f.write(f"{idx}. {row['股票名称']} ({row['股票代码']}) - {row['连板数']}板\n")
                elif pool_type == 'limitdown':
                    top5 = df.nsmallest(5, '涨跌幅')
                    for idx, (_, row) in enumerate(top5.iterrows(), 1):
                        f.write(f"{idx}. {row['股票名称']} ({row['股票代码']}) - {row['涨跌幅']:.2f}%\n")
                else:  # fire
                    top5 = df.nlargest(5, '炸板次数')
                    for idx, (_, row) in enumerate(top5.iterrows(), 1):
                        f.write(f"{idx}. {row['股票名称']} ({row['股票代码']}) - {row['炸板次数']}次炸板\n")

                f.write("\n")

    except Exception as e:
        print(f"❌ 保存汇总统计失败: {e}")


def main():
    """
    主函数
    """
    global FORCE_UPDATE

    print("🚀 TPDog 股票池数据获取工具启动...")
    print(f"🔧 强制更新模式: {'开启' if FORCE_UPDATE else '关闭'}")

    # 1. 加载Token
    token = load_tpdog_token()
    if not token:
        return

    # 2. 选择运行模式
    print("\n请选择运行模式:")
    print("1. 获取单个股票池数据")
    print("2. 获取所有股票池数据并生成排行榜")
    print("3. 查看今日获取记录和缓存信息")
    print("4. 切换强制更新模式")
    print("5. 清理缓存和记录数据")

    try:
        choice = input("请输入选择 (1-5): ").strip()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
        return

    if choice == "1":
        # 单个股票池查询
        print("\n请选择股票池类型:")
        print("1. 涨停池 (limitup)")
        print("2. 跌停池 (limitdown)")
        print("3. 炸板池 (fire)")

        try:
            pool_choice = input("请输入选择 (1-3): ").strip()
            pool_types = {"1": "limitup", "2": "limitdown", "3": "fire"}
            pool_names = {"1": "涨停池", "2": "跌停池", "3": "炸板池"}

            if pool_choice in pool_types:
                pool_type = pool_types[pool_choice]
                pool_name = pool_names[pool_choice]
                print(f"\n📊 正在获取{pool_name}数据...")

                # 获取有效交易日期
                valid_date = get_valid_trading_date(token)
                if valid_date is None:
                    print(f"❌ 无法获取有效的交易日期")
                    return

                data = get_stock_pool_data(token, pool_type, valid_date)
                if data:
                    print(f"✅ 成功获取 {len(data)} 只股票的数据")
                    # 简单打印前几只股票信息
                    for i, stock in enumerate(data[:5], 1):
                        print(
                            f"{i}. {stock.get('name', 'N/A')} ({stock.get('code', 'N/A')}) - {stock.get('rise_rate', 0):.2f}%")
                else:
                    print(f"❌ 未获取到{pool_name}数据")
            else:
                print("❌ 无效选择")
        except KeyboardInterrupt:
            print("\n👋 程序已退出")

    elif choice == "2":
        # 获取所有股票池数据
        print(f"\n📋 正在获取所有股票池数据...")

        all_pools_data = get_all_pools_data(token)

        if all_pools_data:
            # 创建并显示排行榜（只处理非跳过的数据）
            valid_pools_data = {k: v for k, v in all_pools_data.items() if not v.get('skipped', False) and v['data']}

            if valid_pools_data:
                rankings_data = create_rankings(valid_pools_data)

                # 保存数据到文件
                if rankings_data:
                    save_data_to_files(rankings_data)
            else:
                print("📋 所有股票池都已跳过或无数据，未生成新的排行榜")
        else:
            print("❌ 未获取到有效的股票池数据")

    elif choice == "3":
        # 查看今日获取记录和缓存信息
        print_cache_info()

    elif choice == "4":
        # 切换强制更新模式
        current_mode = FORCE_UPDATE
        FORCE_UPDATE = not current_mode
        print(f"🔧 强制更新模式已{'开启' if FORCE_UPDATE else '关闭'}")
        print("⚠️ 注意: 此设置仅在当前运行中生效，重启程序后恢复默认设置")
        if FORCE_UPDATE:
            print("💡 开启强制更新后，即使本地已有文件也会重新获取所有数据")
        else:
            print("💡 关闭强制更新后，收盘时间后会跳过已有数据的股票池")
        # 返回主菜单
        main()
        return

    elif choice == "5":
        # 清理缓存和记录数据
        try:
            confirm = input("确认清理所有缓存和记录数据？(y/N): ").strip().lower()
            if confirm == 'y':
                clear_cache()
            else:
                print("⏭️ 已取消清理操作")
        except KeyboardInterrupt:
            print("\n⏭️ 已取消清理操作")

    else:
        print("❌ 无效选择，程序退出")
        return

    print(f"\n{'=' * 50}")
    print("💡 提示:")
    print("   1. 数据来源于TPDog API，更新频率为交易日收盘后")
    print("   2. 请确保在 .env 文件中正确设置了 TPDOG_TOKEN")
    print("   3. API限制为30次/秒，程序已自动控制请求频率")
    print("   4. 所有数据已按日期保存到 涨停池 文件夹中")
    print("   5. 保存的文件包括:")
    print("      - 各股票池排行榜数据 (CSV)")
    print("      - 原始数据 (JSON)")
    print("      - 汇总统计 (TXT)")
    print("      - 汇总数据 (JSON)")
    print("      - 获取记录 (JSON)")
    print(f"   6. 强制更新模式: {'开启' if FORCE_UPDATE else '关闭'}")
    print("   7. 交易日智能检查:")
    print("      - 自动检查当前日期是否为交易日")
    print("      - 非交易日时自动获取上一个交易日数据")
    print("      - 确保获取到有效的市场数据")
    print("   8. 增量更新功能:")
    print("      - 收盘后(15:00后)会自动跳过已获取成功的股票池")
    print("      - 每次运行都会记录获取状态、时间和股票数量")
    print("      - 开启强制更新可忽略本地文件，重新获取所有数据")
    print("   9. 可通过选项3查看获取记录和本地文件状态")
    print("  10. 可通过选项5清理所有缓存和记录文件")


if __name__ == "__main__":
    main()