import requests
import json
import time
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv
import pandas as pd

# 配置项定义
FORCE_UPDATE = False  # 强制更新开关，默认为False
MARKET_CLOSE_TIME = "15:00"  # 收盘时间定义
RECORD_FILE_NAME = "dragon_tiger_fetch_records.json"  # 记录文件名
DRAGON_TIGER_FOLDER = "dragon_tiger_board"  # 龙虎榜数据文件夹


def load_tpdog_token():
    """
    加载TPDog Token从环境变量
    """
    # 加载 .env 文件中的环境变量
    load_dotenv()

    # 获取TPDOG_TOKEN
    token = os.getenv("TPDOG_TOKEN")

    if not token:
        print("❌ 错误: 未在 .env 文件中找到 TPDOG_TOKEN")
        print("   请在项目根目录创建 .env 文件，并添加: TPDOG_TOKEN=你的token")
        return None

    print(f"✅ 成功加载TPDog Token: {token[:10]}...")
    return token


def is_after_market_close():
    """
    判断当前时间是否为收盘后（15:00后）
    """
    now = datetime.now()
    market_close = datetime.strptime(MARKET_CLOSE_TIME, "%H:%M").time()
    current_time = now.time()

    return current_time > market_close


def get_date_folder(date_str=None):
    """
    获取指定日期的文件夹路径
    """
    if date_str is None:
        date_str = datetime.now().strftime('%Y-%m-%d')
    return os.path.join(DRAGON_TIGER_FOLDER, date_str)


def is_trading_day(token, date_str):
    """
    检查指定日期是否为交易日
    """
    api_url = "https://www.tpdog.com/api/hs/trading_day/is"

    params = {
        'date': date_str,
        'token': token
    }

    try:
        print(f"🔍 正在检查 {date_str} 是否为交易日...")
        response = requests.get(api_url, params=params, timeout=10)
        response.raise_for_status()

        data = response.json()

        if data.get("code") == 1000:
            content = data.get("content", {})
            # 注意：API返回的字段名是 is_trainding（拼写错误是API本身的）
            is_trading = content.get("is_trainding", False)

            if is_trading:
                print(f"✅ {date_str} 是交易日")
            else:
                print(f"❌ {date_str} 不是交易日")

            return is_trading
        else:
            print(f"❌ 检查交易日失败: {data.get('message', '未知错误')}")
            return None

    except Exception as e:
        print(f"❌ 检查交易日失败: {e}")
        return None


def get_previous_trading_day(token, date_str):
    """
    获取指定日期的上一个交易日
    """
    current_date = datetime.strptime(date_str, '%Y-%m-%d')

    print(f"🔍 正在查找上一个交易日（从 {date_str} 开始）...")

    # 最多向前查找15个工作日
    for i in range(1, 16):
        previous_date = current_date - timedelta(days=i)
        previous_date_str = previous_date.strftime('%Y-%m-%d')

        # 检查是否为交易日
        is_trading = is_trading_day(token, previous_date_str)

        if is_trading is None:
            print(f"❌ 检查 {previous_date_str} 交易日状态失败")
            return None

        if is_trading:
            print(f"✅ 找到上一个交易日: {previous_date_str}")
            return previous_date_str

        # 控制请求频率
        time.sleep(0.1)

    print(f"❌ 在过去 15 天内未找到交易日")
    return None


def get_target_date(token, date_str=None):
    """
    获取目标日期（如果不是交易日则获取上一个交易日）
    """
    if date_str is None:
        date_str = datetime.now().strftime('%Y-%m-%d')

    print(f"📅 目标日期: {date_str}")

    # 检查是否为交易日
    is_trading = is_trading_day(token, date_str)

    if is_trading is None:
        print("❌ 无法确定是否为交易日，程序终止")
        return None

    if is_trading:
        return date_str
    else:
        print(f"⚠️ {date_str} 不是交易日，正在查找上一个交易日...")
        return get_previous_trading_day(token, date_str)


def load_fetch_records(date_str):
    """
    加载获取记录文件
    """
    records_file = os.path.join(get_date_folder(date_str), RECORD_FILE_NAME)
    
    if os.path.exists(records_file):
        try:
            with open(records_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠️ 警告: 读取记录文件失败: {e}")
            return {}
    
    return {}


def save_fetch_records(records, date_str):
    """
    保存获取记录文件
    """
    date_dir = get_date_folder(date_str)
    os.makedirs(date_dir, exist_ok=True)
    
    records_file = os.path.join(date_dir, RECORD_FILE_NAME)
    
    try:
        with open(records_file, 'w', encoding='utf-8') as f:
            json.dump(records, f, ensure_ascii=False, indent=2)
        print(f"📝 获取记录已更新: {records_file}")
    except Exception as e:
        print(f"❌ 保存记录文件失败: {e}")


def update_record(records, record_key, status, message=""):
    """
    更新记录
    """
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    if record_key not in records:
        records[record_key] = {}
    
    records[record_key].update({
        'last_fetch_time': timestamp,
        'status': status,  # 'success', 'failed', 'skipped'
        'message': message
    })
    
    return records


def should_skip_fetch(records, record_key):
    """
    判断是否应该跳过数据获取
    - 如果开启强制更新，则不跳过
    - 如果是收盘时间后且本地已有数据，则跳过
    """
    if FORCE_UPDATE:
        return False
    
    if not is_after_market_close():
        return False
    
    # 检查是否已有今日记录
    if record_key in records:
        last_status = records[record_key].get('status')
        if last_status == 'success':
            return True
    
    return False


def get_dragon_tiger_board(token, date_str):
    """
    获取龙虎榜数据
    """
    api_url = "https://www.tpdog.com/api/hs/board/bill"
    
    params = {
        'date': date_str,
        'token': token
    }
    
    try:
        print(f"🔄 正在获取 {date_str} 龙虎榜数据...")
        response = requests.get(api_url, params=params, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        
        if data.get("code") == 1000:
            content = data.get("content", [])
            print(f"✅ 成功获取 {len(content)} 条龙虎榜记录")
            return content
        else:
            print(f"❌ 获取龙虎榜失败: {data.get('message', '未知错误')}")
            return None
            
    except Exception as e:
        print(f"❌ 获取龙虎榜失败: {e}")
        return None


def get_stock_dragon_tiger_history(token, stock_code, start_date, end_date):
    """
    获取个股龙虎榜历史数据
    """
    api_url = "https://www.tpdog.com/api/hs/board/bill_info"
    
    params = {
        'start': start_date,
        'end': end_date,
        'code': stock_code,
        'token': token
    }
    
    try:
        print(f"🔄 正在获取 {stock_code} 龙虎榜历史数据 ({start_date} ~ {end_date})...")
        response = requests.get(api_url, params=params, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        
        if data.get("code") == 1000:
            content = data.get("content", [])
            print(f"✅ 成功获取 {stock_code} {len(content)} 条历史记录")
            return content
        else:
            print(f"❌ 获取 {stock_code} 龙虎榜历史失败: {data.get('message', '未知错误')}")
            return None
            
    except Exception as e:
        print(f"❌ 获取 {stock_code} 龙虎榜历史失败: {e}")
        return None


def save_dragon_tiger_data(data, date_str, data_type="daily"):
    """
    保存龙虎榜数据到文件
    """
    date_dir = get_date_folder(date_str)
    os.makedirs(date_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime('%H-%M')
    
    try:
        if data_type == "daily":
            # 保存当日龙虎榜数据
            filename = f"{timestamp}_dragon_tiger_board_{date_str}.json"
            filepath = os.path.join(date_dir, filename)
            
            save_data = {
                'date': date_str,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_count': len(data),
                'data': data
            }
            
        else:  # stock_history
            # 保存个股历史数据
            stock_code = data_type  # data_type 实际是股票代码
            filename = f"{stock_code}_dragon_tiger_history.json"
            filepath = os.path.join(date_dir, filename)
            
            save_data = {
                'stock_code': stock_code,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_count': len(data),
                'data': data
            }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 数据已保存: {filepath}")
        return filepath
        
    except Exception as e:
        print(f"❌ 保存数据失败: {e}")
        return None


def print_dragon_tiger_summary(data):
    """
    打印龙虎榜数据摘要
    """
    if not data:
        print("❌ 无数据可显示")
        return
    
    print("\n" + "=" * 80)
    print("🐉 龙虎榜数据摘要")
    print("=" * 80)
    
    total_count = len(data)
    print(f"📊 总计上榜股票: {total_count} 只")
    
    # 统计净买入情况
    positive_net = len([item for item in data if item.get('net_amt', 0) > 0])
    negative_net = len([item for item in data if item.get('net_amt', 0) < 0])
    
    print(f"✅ 净买入股票: {positive_net} 只 ({positive_net/total_count*100:.1f}%)")
    print(f"❌ 净卖出股票: {negative_net} 只 ({negative_net/total_count*100:.1f}%)")
    
    # 显示前10名
    sorted_data = sorted(data, key=lambda x: x.get('net_amt', 0), reverse=True)
    
    print(f"\n🏆 龙虎榜净买入前10名:")
    print("-" * 80)
    print(f"{'排名':<4} {'股票代码':<8} {'股票名称':<12} {'净买入额(万元)':<15} {'涨跌幅':<8}")
    print("-" * 80)
    
    for i, item in enumerate(sorted_data[:10], 1):
        code = item.get('code', 'N/A')
        name = item.get('name', 'N/A')
        net_amt = item.get('net_amt', 0) / 10000  # 转换为万元
        rise_rate = item.get('rise_rate', 0)
        
        status = "📈" if rise_rate > 0 else "📉" if rise_rate < 0 else "➡️"
        
        print(f"{i:<4} {code:<8} {name:<12} {net_amt:>12,.0f} {status} {rise_rate:>6.2f}%")
    
    print("=" * 80)


def get_hot_money_list(token):
    """
    获取游资列表
    """
    api_url = "https://www.tpdog.com/api/hs/hot_money/list"

    params = {
        'token': token
    }

    try:
        print(f"🔄 正在获取游资列表...")
        response = requests.get(api_url, params=params, timeout=10)
        response.raise_for_status()

        data = response.json()

        if data.get("code") == 1000:
            content = data.get("content", [])
            print(f"✅ 成功获取 {len(content)} 个游资")
            return content
        else:
            print(f"❌ 获取游资列表失败: {data.get('message', '未知错误')}")
            return None

    except Exception as e:
        print(f"❌ 获取游资列表失败: {e}")
        return None


def get_daily_hot_money(token, date_str):
    """
    获取每日游资数据
    """
    api_url = "https://www.tpdog.com/api/hs/hot_money/daily/list_date"

    params = {
        'date': date_str,
        'token': token
    }

    try:
        print(f"🔄 正在获取 {date_str} 每日游资数据...")
        response = requests.get(api_url, params=params, timeout=10)
        response.raise_for_status()

        data = response.json()

        if data.get("code") == 1000:
            content = data.get("content", [])
            print(f"✅ 成功获取 {len(content)} 条每日游资记录")
            return content
        else:
            print(f"❌ 获取每日游资失败: {data.get('message', '未知错误')}")
            return None

    except Exception as e:
        print(f"❌ 获取每日游资失败: {e}")
        return None


def get_hot_money_history(token, hot_code, start_date, end_date):
    """
    获取游资历史数据
    """
    api_url = "https://www.tpdog.com/api/hs/hot_money/daily/list_code"

    params = {
        'hot_code': hot_code,
        'start': start_date,
        'end': end_date,
        'token': token
    }

    try:
        print(f"🔄 正在获取游资 {hot_code} 历史数据 ({start_date} ~ {end_date})...")
        response = requests.get(api_url, params=params, timeout=10)
        response.raise_for_status()

        data = response.json()

        if data.get("code") == 1000:
            content = data.get("content", [])
            print(f"✅ 成功获取游资 {hot_code} {len(content)} 条历史记录")
            return content
        else:
            print(f"❌ 获取游资历史失败: {data.get('message', '未知错误')}")
            return None

    except Exception as e:
        print(f"❌ 获取游资历史失败: {e}")
        return None


def print_daily_hot_money_summary(data):
    """
    打印每日游资数据摘要
    """
    if not data:
        print("❌ 无每日游资数据可显示")
        return

    print("\n" + "=" * 80)
    print("💰 每日游资数据摘要")
    print("=" * 80)

    total_count = len(data)
    print(f"📊 总计游资操作: {total_count} 笔")

    # 统计净买入情况
    positive_net = len([item for item in data if item.get('net', 0) > 0])
    negative_net = len([item for item in data if item.get('net', 0) < 0])

    print(f"✅ 净买入操作: {positive_net} 笔 ({positive_net/total_count*100:.1f}%)")
    print(f"❌ 净卖出操作: {negative_net} 笔 ({negative_net/total_count*100:.1f}%)")

    # 显示前10名
    sorted_data = sorted(data, key=lambda x: x.get('net', 0), reverse=True)

    print(f"\n🏆 游资净买入前10名:")
    print("-" * 80)
    print(f"{'排名':<4} {'股票代码':<8} {'股票名称':<12} {'游资名称':<15} {'净买入额(万元)':<15} {'涨跌幅':<8}")
    print("-" * 80)

    for i, item in enumerate(sorted_data[:10], 1):
        code = item.get('code', 'N/A')
        name = item.get('name', 'N/A')
        hot_name = item.get('hot_name', 'N/A')
        net_amt = item.get('net', 0) / 10000  # 转换为万元
        rise_rate = item.get('rise_rate', 0)

        status = "📈" if rise_rate > 0 else "📉" if rise_rate < 0 else "➡️"

        print(f"{i:<4} {code:<8} {name:<12} {hot_name:<15} {net_amt:>12,.0f} {status} {rise_rate:>6.2f}%")

    print("=" * 80)


def print_stock_dragon_tiger_history(data, stock_code):
    """
    打印个股龙虎榜历史数据
    """
    if not data:
        print(f"❌ {stock_code} 无历史龙虎榜数据")
        return

    print("\n" + "=" * 80)
    print(f"📈 {stock_code} 龙虎榜历史数据")
    print("=" * 80)

    total_count = len(data)
    print(f"📊 历史上榜次数: {total_count} 次")

    # 按日期排序
    sorted_data = sorted(data, key=lambda x: x.get('date', ''), reverse=True)

    print(f"\n📅 最近上榜记录:")
    print("-" * 80)
    print(f"{'日期':<12} {'收盘价':<8} {'涨跌幅':<8} {'净买入额(万元)':<15} {'上榜原因':<20}")
    print("-" * 80)

    for item in sorted_data[:10]:  # 显示最近10次
        date = item.get('date', 'N/A')
        close = item.get('close', 0)
        rise_rate = item.get('rise_rate', 0)
        net_amt = item.get('net_amt', 0) / 10000  # 转换为万元
        reason = item.get('reason', 'N/A')[:18]  # 截断过长的原因

        status = "📈" if rise_rate > 0 else "📉" if rise_rate < 0 else "➡️"

        print(f"{date:<12} {close:<8.2f} {status} {rise_rate:>6.2f}% {net_amt:>12,.0f} {reason:<20}")

    print("=" * 80)


def print_hot_money_history(data, hot_code, hot_name):
    """
    打印游资历史数据
    """
    if not data:
        print(f"❌ {hot_name}({hot_code}) 无历史数据")
        return

    print("\n" + "=" * 80)
    print(f"📈 {hot_name}({hot_code}) 历史数据")
    print("=" * 80)

    total_count = len(data)
    print(f"📊 历史操作次数: {total_count} 次")

    # 按日期排序
    sorted_data = sorted(data, key=lambda x: x.get('date', ''), reverse=True)

    print(f"\n📅 最近操作记录:")
    print("-" * 80)
    print(f"{'日期':<12} {'股票代码':<8} {'股票名称':<12} {'净买入额(万元)':<15} {'涨跌幅':<8} {'上榜原因':<20}")
    print("-" * 80)

    for item in sorted_data[:10]:  # 显示最近10次
        date = item.get('date', 'N/A')
        code = item.get('code', 'N/A')
        name = item.get('name', 'N/A')
        net_amt = item.get('net', 0) / 10000  # 转换为万元
        rise_rate = item.get('rise_rate', 0)
        reason = item.get('reason', 'N/A')[:18]  # 截断过长的原因

        status = "📈" if rise_rate > 0 else "📉" if rise_rate < 0 else "➡️"

        print(f"{date:<12} {code:<8} {name:<12} {net_amt:>12,.0f} {status} {rise_rate:>6.2f}% {reason:<20}")

    print("=" * 80)


def save_hot_money_data(data, date_str, data_type="daily", hot_name=None):
    """
    保存游资数据到文件
    """
    date_dir = get_date_folder(date_str)
    os.makedirs(date_dir, exist_ok=True)

    timestamp = datetime.now().strftime('%H-%M')

    try:
        if data_type == "daily":
            # 保存每日游资数据
            filename = f"{timestamp}_daily_hot_money_{date_str}.json"
            filepath = os.path.join(date_dir, filename)

            save_data = {
                'date': date_str,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_count': len(data),
                'data_type': 'daily_hot_money',
                'data': data
            }

        else:  # hot_money_history
            # 保存游资历史数据
            safe_name = hot_name.replace('/', '_').replace('\\', '_') if hot_name else 'unknown'
            filename = f"{safe_name}_hot_money_history.json"
            filepath = os.path.join(date_dir, filename)

            save_data = {
                'hot_name': hot_name,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_count': len(data),
                'data_type': 'hot_money_history',
                'data': data
            }

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)

        print(f"💾 游资数据已保存: {filepath}")
        return filepath

    except Exception as e:
        print(f"❌ 保存游资数据失败: {e}")
        return None


def fetch_all_dragon_tiger_stocks(token, date_str):
    """
    获取指定日期所有龙虎榜股票的历史数据
    """
    # 先获取当日龙虎榜
    dragon_tiger_data = get_dragon_tiger_board(token, date_str)

    if not dragon_tiger_data:
        print("❌ 无法获取龙虎榜数据")
        return

    # 保存当日龙虎榜数据
    save_dragon_tiger_data(dragon_tiger_data, date_str, "daily")

    # 加载获取记录
    records = load_fetch_records(date_str)

    print(f"\n🔄 开始获取所有龙虎榜股票的历史数据...")

    success_count = 0
    failed_count = 0
    skipped_count = 0

    for i, stock_data in enumerate(dragon_tiger_data, 1):
        stock_code = stock_data.get('code', '')
        stock_name = stock_data.get('name', '')

        if not stock_code:
            continue

        # 构造完整股票代码（添加市场前缀）
        if stock_code.startswith('0') or stock_code.startswith('3'):
            full_code = f"sz.{stock_code}"
        else:
            full_code = f"sh.{stock_code}"

        record_key = f"stock_history_{full_code}"

        # 检查是否应该跳过
        if should_skip_fetch(records, record_key):
            print(f"⏭️ 跳过 {stock_name}({stock_code}) 历史数据获取... ({i}/{len(dragon_tiger_data)})")
            skipped_count += 1
            records = update_record(records, record_key, 'skipped', '已有数据，跳过获取')
            continue

        print(f"🔄 正在获取 {stock_name}({stock_code}) 历史数据... ({i}/{len(dragon_tiger_data)})")

        # 获取最近30天的历史数据
        end_date = date_str
        start_date = (datetime.strptime(date_str, '%Y-%m-%d') - timedelta(days=30)).strftime('%Y-%m-%d')

        history_data = get_stock_dragon_tiger_history(token, full_code, start_date, end_date)

        if history_data:
            # 保存个股历史数据
            save_dragon_tiger_data(history_data, date_str, stock_code)
            success_count += 1
            records = update_record(records, record_key, 'success', f'获取{len(history_data)}条历史记录')
        else:
            failed_count += 1
            records = update_record(records, record_key, 'failed', '历史数据获取失败')

        # 控制请求频率
        if i < len(dragon_tiger_data):
            time.sleep(0.1)  # 等待100ms，确保不超过30次/秒的限制

    # 保存获取记录
    save_fetch_records(records, date_str)

    print(f"\n📈 获取统计:")
    print(f"✅ 成功获取: {success_count} 只股票")
    print(f"⏭️ 跳过股票: {skipped_count} 只股票")
    print(f"❌ 获取失败: {failed_count} 只股票")
    print(f"📊 总计股票: {len(dragon_tiger_data)} 只股票")


def print_fetch_records(date_str):
    """
    打印获取记录统计
    """
    records = load_fetch_records(date_str)

    if not records:
        print(f"📝 {date_str} 暂无获取记录")
        return

    print("\n" + "=" * 60)
    print(f"📝 {date_str} 获取记录统计")
    print("=" * 60)

    success_count = 0
    failed_count = 0
    skipped_count = 0

    for record_key, record in records.items():
        status = record.get('status', 'unknown')
        if status == 'success':
            success_count += 1
        elif status == 'failed':
            failed_count += 1
        elif status == 'skipped':
            skipped_count += 1

    print(f"✅ 成功获取: {success_count} 项")
    print(f"❌ 获取失败: {failed_count} 项")
    print(f"⏭️ 跳过项目: {skipped_count} 项")
    print(f"📊 总计记录: {len(records)} 项")

    # 显示最近的几条记录
    print(f"\n最近5条记录:")
    print("-" * 60)

    sorted_records = sorted(records.items(),
                           key=lambda x: x[1].get('last_fetch_time', ''),
                           reverse=True)

    for record_key, record in sorted_records[:5]:
        status_icon = {'success': '✅', 'failed': '❌', 'skipped': '⏭️'}.get(record.get('status'), '❓')
        print(f"{status_icon} {record_key:<30} {record.get('last_fetch_time', 'N/A'):<20} {record.get('message', 'N/A')}")


def main():
    """
    主函数
    """
    global FORCE_UPDATE

    print("🚀 TPDog 龙虎榜数据获取工具启动...")
    print(f"🔧 强制更新模式: {'开启' if FORCE_UPDATE else '关闭'}")

    # 1. 加载Token
    token = load_tpdog_token()
    if not token:
        return

    # 2. 选择运行模式
    print("\n请选择运行模式:")
    print("1. 获取当日龙虎榜数据")
    print("2. 获取指定日期龙虎榜数据")
    print("3. 获取当日龙虎榜 + 所有股票历史数据")
    print("4. 获取指定股票龙虎榜历史")
    print("5. 获取当日龙虎榜 + 每日游资数据")
    print("6. 获取游资历史数据")
    print("7. 查看获取记录")
    print("8. 切换强制更新模式")

    try:
        choice = input("请输入选择 (1-8): ").strip()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
        return

    if choice == "1":
        # 获取当日龙虎榜
        target_date = get_target_date(token)
        if target_date:
            data = get_dragon_tiger_board(token, target_date)
            if data:
                print_dragon_tiger_summary(data)
                save_dragon_tiger_data(data, target_date, "daily")

    elif choice == "2":
        # 获取指定日期龙虎榜
        try:
            date_input = input("请输入日期 (格式: YYYY-MM-DD): ").strip()
            target_date = get_target_date(token, date_input)
            if target_date:
                data = get_dragon_tiger_board(token, target_date)
                if data:
                    print_dragon_tiger_summary(data)
                    save_dragon_tiger_data(data, target_date, "daily")
        except KeyboardInterrupt:
            print("\n👋 操作已取消")

    elif choice == "3":
        # 获取当日龙虎榜 + 所有股票历史
        target_date = get_target_date(token)
        if target_date:
            fetch_all_dragon_tiger_stocks(token, target_date)

    elif choice == "4":
        # 获取指定股票历史
        try:
            stock_code = input("请输入股票代码 (如: sz.000001): ").strip()
            start_date = input("请输入开始日期 (格式: YYYY-MM-DD): ").strip()
            end_date = input("请输入结束日期 (格式: YYYY-MM-DD): ").strip()

            data = get_stock_dragon_tiger_history(token, stock_code, start_date, end_date)
            if data:
                print_stock_dragon_tiger_history(data, stock_code)
                save_dragon_tiger_data(data, end_date, stock_code.split('.')[1])
        except KeyboardInterrupt:
            print("\n👋 操作已取消")

    elif choice == "5":
        # 获取当日龙虎榜 + 每日游资数据
        target_date = get_target_date(token)
        if target_date:
            # 获取龙虎榜数据
            dragon_data = get_dragon_tiger_board(token, target_date)
            if dragon_data:
                print_dragon_tiger_summary(dragon_data)
                save_dragon_tiger_data(dragon_data, target_date, "daily")

            # 获取每日游资数据
            hot_money_data = get_daily_hot_money(token, target_date)
            if hot_money_data:
                print_daily_hot_money_summary(hot_money_data)
                save_hot_money_data(hot_money_data, target_date, "daily")

    elif choice == "6":
        # 获取游资历史数据
        try:
            # 先获取游资列表
            hot_money_list = get_hot_money_list(token)
            if not hot_money_list:
                print("❌ 无法获取游资列表")
                return

            # 显示游资列表供选择
            print(f"\n📋 游资列表 (共{len(hot_money_list)}个):")
            print("-" * 50)
            for i, hot_money in enumerate(hot_money_list[:20], 1):  # 显示前20个
                print(f"{i:2d}. {hot_money.get('hot_name', 'N/A')} ({hot_money.get('hot_code', 'N/A')})")

            if len(hot_money_list) > 20:
                print(f"... 还有 {len(hot_money_list) - 20} 个游资")

            # 让用户选择游资
            hot_code = input("\n请输入游资代码 (如: 00002): ").strip()
            start_date = input("请输入开始日期 (格式: YYYY-MM-DD): ").strip()
            end_date = input("请输入结束日期 (格式: YYYY-MM-DD): ").strip()

            # 查找游资名称
            hot_name = "未知游资"
            for hm in hot_money_list:
                if hm.get('hot_code') == hot_code:
                    hot_name = hm.get('hot_name', '未知游资')
                    break

            # 获取游资历史数据
            history_data = get_hot_money_history(token, hot_code, start_date, end_date)
            if history_data:
                print_hot_money_history(history_data, hot_code, hot_name)
                save_hot_money_data(history_data, end_date, "history", hot_name)
        except KeyboardInterrupt:
            print("\n👋 操作已取消")

    elif choice == "7":
        # 查看获取记录
        try:
            date_input = input("请输入日期 (格式: YYYY-MM-DD，回车查看今日): ").strip()
            if not date_input:
                date_input = datetime.now().strftime('%Y-%m-%d')
            print_fetch_records(date_input)
        except KeyboardInterrupt:
            print("\n👋 操作已取消")

    elif choice == "8":
        # 切换强制更新模式
        current_mode = FORCE_UPDATE
        FORCE_UPDATE = not current_mode
        print(f"🔧 强制更新模式已{'开启' if FORCE_UPDATE else '关闭'}")
        print("⚠️ 注意: 此设置仅在当前运行中生效，重启程序后恢复默认设置")
        # 返回主菜单
        main()
        return

    else:
        print("❌ 无效选择，程序退出")
        return

    print(f"\n{'=' * 50}")
    print("💡 提示:")
    print("   1. 数据来源于TPDog API，请求频率限制为30次/秒")
    print("   2. 请确保在 .env 文件中正确设置了 TPDOG_TOKEN")
    print("   3. 程序会自动检查是否为交易日，非交易日会获取上一个交易日数据")
    print("   4. 所有数据按日期保存到 dragon_tiger_board 文件夹中")
    print("   5. 保存的文件包括:")
    print("      - 当日龙虎榜数据 (JSON)")
    print("      - 个股龙虎榜历史数据 (JSON)")
    print("      - 每日游资数据 (JSON)")
    print("      - 游资历史数据 (JSON)")
    print("      - 获取记录 (JSON)")
    print(f"   6. 强制更新模式: {'开启' if FORCE_UPDATE else '关闭'}")
    print("   7. 收盘后(15:00后)会自动跳过已获取的数据")
    print("   8. 每次运行都会记录获取状态和时间")
    print("   9. 游资数据包括每日操作和历史记录")
    print("  10. 游资历史数据按游资名称保存")


if __name__ == "__main__":
    main()
