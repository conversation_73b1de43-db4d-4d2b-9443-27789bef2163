{"bkc.880363": {"sector_name": "财税数字化", "last_fetch_time": "2025-07-12 22:57:26", "status": "success", "message": "数据获取成功"}, "bkc.880368": {"sector_name": "玻璃基板", "last_fetch_time": "2025-07-12 22:57:26", "status": "success", "message": "数据获取成功"}, "bkc.880371": {"sector_name": "合成生物", "last_fetch_time": "2025-07-12 22:57:26", "status": "success", "message": "数据获取成功"}, "bkc.880372": {"sector_name": "锂矿概念", "last_fetch_time": "2025-07-12 22:57:27", "status": "success", "message": "数据获取成功"}, "bkc.880373": {"sector_name": "AI语料", "last_fetch_time": "2025-07-12 22:57:27", "status": "success", "message": "数据获取成功"}, "bkc.880374": {"sector_name": "碳纤维", "last_fetch_time": "2025-07-12 22:57:27", "status": "success", "message": "数据获取成功"}, "bkc.880377": {"sector_name": "AI制药（医疗）", "last_fetch_time": "2025-07-12 22:57:28", "status": "success", "message": "数据获取成功"}, "bkc.880379": {"sector_name": "<PERSON><PERSON>概念", "last_fetch_time": "2025-07-12 22:57:28", "status": "success", "message": "数据获取成功"}, "bkc.880384": {"sector_name": "铜缆高速连接", "last_fetch_time": "2025-07-12 22:57:28", "status": "success", "message": "数据获取成功"}, "bkc.880389": {"sector_name": "生物医药", "last_fetch_time": "2025-07-12 22:57:29", "status": "success", "message": "数据获取成功"}, "bkc.880394": {"sector_name": "低空经济", "last_fetch_time": "2025-07-12 22:57:29", "status": "success", "message": "数据获取成功"}, "bkc.880398": {"sector_name": "娃哈哈概念", "last_fetch_time": "2025-07-12 22:57:29", "status": "success", "message": "数据获取成功"}, "bkc.880399": {"sector_name": "AIPC", "last_fetch_time": "2025-07-12 22:57:30", "status": "success", "message": "数据获取成功"}, "bkc.880404": {"sector_name": "可控核聚变", "last_fetch_time": "2025-07-12 22:57:30", "status": "success", "message": "数据获取成功"}, "bkc.880407": {"sector_name": "AI手机", "last_fetch_time": "2025-07-12 22:57:31", "status": "success", "message": "数据获取成功"}, "bkc.880408": {"sector_name": "英伟达概念", "last_fetch_time": "2025-07-12 22:57:31", "status": "success", "message": "数据获取成功"}, "bkc.880409": {"sector_name": "柔性屏(折叠屏)", "last_fetch_time": "2025-07-12 22:57:31", "status": "success", "message": "数据获取成功"}, "bkc.880412": {"sector_name": "Sora概念", "last_fetch_time": "2025-07-12 22:57:32", "status": "success", "message": "数据获取成功"}, "bkc.880416": {"sector_name": "微盘股", "last_fetch_time": "2025-07-12 22:57:32", "status": "success", "message": "数据获取成功"}, "bkc.880419": {"sector_name": "飞行汽车(eVTOL)", "last_fetch_time": "2025-07-12 22:57:32", "status": "success", "message": "数据获取成功"}, "bkc.880422": {"sector_name": "PEEK材料概念", "last_fetch_time": "2025-07-12 22:57:33", "status": "success", "message": "数据获取成功"}, "bkc.880425": {"sector_name": "小米汽车", "last_fetch_time": "2025-07-12 22:57:33", "status": "success", "message": "数据获取成功"}, "bkc.880426": {"sector_name": "东盟自贸区概念", "last_fetch_time": "2025-07-12 22:57:33", "status": "success", "message": "数据获取成功"}, "bkc.880428": {"sector_name": "多模态AI", "last_fetch_time": "2025-07-12 22:57:34", "status": "success", "message": "数据获取成功"}, "bkc.880430": {"sector_name": "高带宽内存", "last_fetch_time": "2025-07-12 22:57:34", "status": "success", "message": "数据获取成功"}, "bkc.880431": {"sector_name": "短剧互动游戏", "last_fetch_time": "2025-07-12 22:57:34", "status": "success", "message": "数据获取成功"}, "bkc.880435": {"sector_name": "新型工业化", "last_fetch_time": "2025-07-12 22:57:35", "status": "success", "message": "数据获取成功"}, "bkc.880438": {"sector_name": "星闪概念", "last_fetch_time": "2025-07-12 22:57:36", "status": "success", "message": "数据获取成功"}, "bkc.880441": {"sector_name": "BC电池", "last_fetch_time": "2025-07-12 22:57:36", "status": "success", "message": "数据获取成功"}, "bkc.880445": {"sector_name": "SPD概念", "last_fetch_time": "2025-07-12 22:57:36", "status": "success", "message": "数据获取成功"}, "bkc.880446": {"sector_name": "减肥药", "last_fetch_time": "2025-07-12 22:57:37", "status": "success", "message": "数据获取成功"}, "bkc.880448": {"sector_name": "机器人执行器", "last_fetch_time": "2025-07-12 22:57:37", "status": "success", "message": "数据获取成功"}, "bkc.880450": {"sector_name": "高压快充", "last_fetch_time": "2025-07-12 22:57:37", "status": "success", "message": "数据获取成功"}, "bkc.880451": {"sector_name": "空间计算", "last_fetch_time": "2025-07-12 22:57:37", "status": "success", "message": "数据获取成功"}, "bkc.880453": {"sector_name": "裸眼3D", "last_fetch_time": "2025-07-12 22:57:38", "status": "success", "message": "数据获取成功"}, "bkc.880458": {"sector_name": "混合现实", "last_fetch_time": "2025-07-12 22:57:38", "status": "success", "message": "数据获取成功"}, "bkc.880460": {"sector_name": "央企改革", "last_fetch_time": "2025-07-12 22:57:38", "status": "success", "message": "数据获取成功"}, "bkc.880465": {"sector_name": "中特估", "last_fetch_time": "2025-07-12 22:57:39", "status": "success", "message": "数据获取成功"}, "bkc.880468": {"sector_name": "液冷概念", "last_fetch_time": "2025-07-12 22:57:39", "status": "success", "message": "数据获取成功"}, "bkc.880469": {"sector_name": "存储芯片", "last_fetch_time": "2025-07-12 22:57:39", "status": "success", "message": "数据获取成功"}, "bkc.880471": {"sector_name": "光通信模块", "last_fetch_time": "2025-07-12 22:57:40", "status": "success", "message": "数据获取成功"}, "bkc.880476": {"sector_name": "数据要素", "last_fetch_time": "2025-07-12 22:57:41", "status": "success", "message": "数据获取成功"}, "bkc.880479": {"sector_name": "算力概念", "last_fetch_time": "2025-07-12 22:57:41", "status": "success", "message": "数据获取成功"}, "bkc.880483": {"sector_name": "ERP概念", "last_fetch_time": "2025-07-12 22:57:41", "status": "success", "message": "数据获取成功"}, "bkc.880484": {"sector_name": "MLOps概念", "last_fetch_time": "2025-07-12 22:57:42", "status": "success", "message": "数据获取成功"}, "bkc.880487": {"sector_name": "同步磁阻电机", "last_fetch_time": "2025-07-12 22:57:42", "status": "success", "message": "数据获取成功"}, "bkc.880488": {"sector_name": "时空大数据", "last_fetch_time": "2025-07-12 22:57:42", "status": "success", "message": "数据获取成功"}, "bkc.880489": {"sector_name": "数字水印", "last_fetch_time": "2025-07-12 22:57:43", "status": "success", "message": "数据获取成功"}, "bkc.880493": {"sector_name": "CPO概念", "last_fetch_time": "2025-07-12 22:57:43", "status": "success", "message": "数据获取成功"}, "bkc.880497": {"sector_name": "AI芯片", "last_fetch_time": "2025-07-12 22:57:43", "status": "success", "message": "数据获取成功"}, "bkc.880502": {"sector_name": "ChatGPT概念", "last_fetch_time": "2025-07-12 22:57:44", "status": "success", "message": "数据获取成功"}, "bkc.880503": {"sector_name": "电子后视镜", "last_fetch_time": "2025-07-12 22:57:44", "status": "success", "message": "数据获取成功"}, "bkc.880506": {"sector_name": "毫米波概念", "last_fetch_time": "2025-07-12 22:57:44", "status": "success", "message": "数据获取成功"}, "bkc.880510": {"sector_name": "蒙脱石散", "last_fetch_time": "2025-07-12 22:57:45", "status": "success", "message": "数据获取成功"}, "bkc.880515": {"sector_name": "血氧仪", "last_fetch_time": "2025-07-12 22:57:45", "status": "success", "message": "数据获取成功"}, "bkc.880519": {"sector_name": "第四代半导体", "last_fetch_time": "2025-07-12 22:57:46", "status": "success", "message": "数据获取成功"}, "bkc.880523": {"sector_name": "熊去氧胆酸", "last_fetch_time": "2025-07-12 22:57:46", "status": "success", "message": "数据获取成功"}, "bkc.880525": {"sector_name": "PLC概念", "last_fetch_time": "2025-07-12 22:57:46", "status": "success", "message": "数据获取成功"}, "bkc.880528": {"sector_name": "数据确权", "last_fetch_time": "2025-07-12 22:57:47", "status": "success", "message": "数据获取成功"}, "bkc.880529": {"sector_name": "抗原检测", "last_fetch_time": "2025-07-12 22:57:47", "status": "success", "message": "数据获取成功"}, "bkc.880530": {"sector_name": "抗菌面料", "last_fetch_time": "2025-07-12 22:57:47", "status": "success", "message": "数据获取成功"}, "bkc.880531": {"sector_name": "跨境电商", "last_fetch_time": "2025-07-12 22:57:48", "status": "success", "message": "数据获取成功"}, "bkc.880532": {"sector_name": "人造太阳", "last_fetch_time": "2025-07-12 22:57:48", "status": "success", "message": "数据获取成功"}, "bkc.880535": {"sector_name": "复合集流体", "last_fetch_time": "2025-07-12 22:57:48", "status": "success", "message": "数据获取成功"}, "bkc.880537": {"sector_name": "破净股", "last_fetch_time": "2025-07-12 22:57:49", "status": "success", "message": "数据获取成功"}, "bkc.880540": {"sector_name": "AIGC概念", "last_fetch_time": "2025-07-12 22:57:49", "status": "success", "message": "数据获取成功"}, "bkc.880544": {"sector_name": "Web3.0", "last_fetch_time": "2025-07-12 22:57:49", "status": "success", "message": "数据获取成功"}, "bkc.880546": {"sector_name": "供销社概念", "last_fetch_time": "2025-07-12 22:57:50", "status": "success", "message": "数据获取成功"}, "bkc.880550": {"sector_name": "科创板做市股", "last_fetch_time": "2025-07-12 22:57:50", "status": "success", "message": "数据获取成功"}, "bkc.880555": {"sector_name": "科创板做市商", "last_fetch_time": "2025-07-12 22:57:51", "status": "success", "message": "数据获取成功"}, "bkc.880558": {"sector_name": "创新药", "last_fetch_time": "2025-07-12 22:57:51", "status": "success", "message": "数据获取成功"}, "bkc.880560": {"sector_name": "世界杯", "last_fetch_time": "2025-07-12 22:57:51", "status": "success", "message": "数据获取成功"}, "bkc.880565": {"sector_name": "信创", "last_fetch_time": "2025-07-12 22:57:52", "status": "success", "message": "数据获取成功"}, "bkc.880566": {"sector_name": "熔盐储能", "last_fetch_time": "2025-07-12 22:57:52", "status": "success", "message": "数据获取成功"}, "bkc.880567": {"sector_name": "空气能热泵", "last_fetch_time": "2025-07-12 22:57:52", "status": "success", "message": "数据获取成功"}, "bkc.880572": {"sector_name": "Chiplet概念", "last_fetch_time": "2025-07-12 22:57:53", "status": "success", "message": "数据获取成功"}, "bkc.880575": {"sector_name": "减速器", "last_fetch_time": "2025-07-12 22:57:53", "status": "success", "message": "数据获取成功"}, "bkc.880577": {"sector_name": "轮毂电机", "last_fetch_time": "2025-07-12 22:57:53", "status": "success", "message": "数据获取成功"}, "bkc.880581": {"sector_name": "生物质能发电", "last_fetch_time": "2025-07-12 22:57:54", "status": "success", "message": "数据获取成功"}, "bkc.880586": {"sector_name": "TOPCon电池", "last_fetch_time": "2025-07-12 22:57:54", "status": "success", "message": "数据获取成功"}, "bkc.880590": {"sector_name": "光伏高速公路", "last_fetch_time": "2025-07-12 22:57:54", "status": "success", "message": "数据获取成功"}, "bkc.880593": {"sector_name": "钒电池", "last_fetch_time": "2025-07-12 22:57:55", "status": "success", "message": "数据获取成功"}, "bkc.880597": {"sector_name": "钙钛矿电池", "last_fetch_time": "2025-07-12 22:57:55", "status": "success", "message": "数据获取成功"}, "bkc.880598": {"sector_name": "汽车一体化压铸", "last_fetch_time": "2025-07-12 22:57:56", "status": "success", "message": "数据获取成功"}, "bkc.880600": {"sector_name": "麒麟电池", "last_fetch_time": "2025-07-12 22:57:56", "status": "success", "message": "数据获取成功"}, "bkc.880603": {"sector_name": "机器人概念", "last_fetch_time": "2025-07-12 22:57:56", "status": "success", "message": "数据获取成功"}, "bkc.880606": {"sector_name": "汽车热管理", "last_fetch_time": "2025-07-12 22:57:57", "status": "success", "message": "数据获取成功"}, "bkc.880609": {"sector_name": "F5G概念", "last_fetch_time": "2025-07-12 22:57:57", "status": "success", "message": "数据获取成功"}, "bkc.880611": {"sector_name": "超超临界发电", "last_fetch_time": "2025-07-12 22:57:57", "status": "success", "message": "数据获取成功"}, "bkc.880616": {"sector_name": "粮食概念", "last_fetch_time": "2025-07-12 22:57:58", "status": "success", "message": "数据获取成功"}, "bkc.880617": {"sector_name": "痘病毒防治", "last_fetch_time": "2025-07-12 22:57:58", "status": "success", "message": "数据获取成功"}, "bkc.880620": {"sector_name": "数字哨兵", "last_fetch_time": "2025-07-12 22:57:58", "status": "success", "message": "数据获取成功"}, "bkc.880622": {"sector_name": "千金藤素", "last_fetch_time": "2025-07-12 22:57:59", "status": "success", "message": "数据获取成功"}, "bkc.880624": {"sector_name": "噪声防治", "last_fetch_time": "2025-07-12 22:57:59", "status": "success", "message": "数据获取成功"}, "bkc.880625": {"sector_name": "新型城镇化", "last_fetch_time": "2025-07-12 22:57:59", "status": "success", "message": "数据获取成功"}, "bkc.880629": {"sector_name": "户外露营", "last_fetch_time": "2025-07-12 22:58:00", "status": "success", "message": "数据获取成功"}, "bkc.880633": {"sector_name": "肝炎概念", "last_fetch_time": "2025-07-12 22:58:00", "status": "success", "message": "数据获取成功"}, "bkc.880634": {"sector_name": "统一大市场", "last_fetch_time": "2025-07-12 22:58:01", "status": "success", "message": "数据获取成功"}, "bkc.880638": {"sector_name": "建筑节能", "last_fetch_time": "2025-07-12 22:58:01", "status": "success", "message": "数据获取成功"}, "bkc.880639": {"sector_name": "电子身份证", "last_fetch_time": "2025-07-12 22:58:02", "status": "success", "message": "数据获取成功"}, "bkc.880641": {"sector_name": "托育服务", "last_fetch_time": "2025-07-12 22:58:02", "status": "success", "message": "数据获取成功"}, "bkc.880644": {"sector_name": "啤酒概念", "last_fetch_time": "2025-07-12 22:58:02", "status": "success", "message": "数据获取成功"}, "bkc.880649": {"sector_name": "中俄贸易概念", "last_fetch_time": "2025-07-12 22:58:03", "status": "success", "message": "数据获取成功"}, "bkc.880653": {"sector_name": "跨境支付", "last_fetch_time": "2025-07-12 22:58:03", "status": "success", "message": "数据获取成功"}, "bkc.880658": {"sector_name": "土壤修复", "last_fetch_time": "2025-07-12 22:58:03", "status": "success", "message": "数据获取成功"}, "bkc.880663": {"sector_name": "智慧灯杆", "last_fetch_time": "2025-07-12 22:58:04", "status": "success", "message": "数据获取成功"}, "bkc.880664": {"sector_name": "净水概念", "last_fetch_time": "2025-07-12 22:58:04", "status": "success", "message": "数据获取成功"}, "bkc.880668": {"sector_name": "杭州亚运会", "last_fetch_time": "2025-07-12 22:58:04", "status": "success", "message": "数据获取成功"}, "bkc.880672": {"sector_name": "民爆概念", "last_fetch_time": "2025-07-12 22:58:04", "status": "success", "message": "数据获取成功"}, "bkc.880676": {"sector_name": "气溶胶检测", "last_fetch_time": "2025-07-12 22:58:05", "status": "success", "message": "数据获取成功"}, "bkc.880679": {"sector_name": "东数西算", "last_fetch_time": "2025-07-12 22:58:06", "status": "success", "message": "数据获取成功"}, "bkc.880682": {"sector_name": "重组蛋白", "last_fetch_time": "2025-07-12 22:58:06", "status": "success", "message": "数据获取成功"}, "bkc.880687": {"sector_name": "新冠检测", "last_fetch_time": "2025-07-12 22:58:06", "status": "success", "message": "数据获取成功"}, "bkc.880688": {"sector_name": "数字经济", "last_fetch_time": "2025-07-12 22:58:07", "status": "success", "message": "数据获取成功"}, "bkc.880692": {"sector_name": "新冠药物", "last_fetch_time": "2025-07-12 22:58:07", "status": "success", "message": "数据获取成功"}, "bkc.880694": {"sector_name": "百元股", "last_fetch_time": "2025-07-12 22:58:07", "status": "success", "message": "数据获取成功"}, "bkc.880696": {"sector_name": "地下管网", "last_fetch_time": "2025-07-12 22:58:08", "status": "success", "message": "数据获取成功"}, "bkc.880701": {"sector_name": "电子纸概念", "last_fetch_time": "2025-07-12 22:58:08", "status": "success", "message": "数据获取成功"}, "bkc.880704": {"sector_name": "幽门螺杆菌概念", "last_fetch_time": "2025-07-12 22:58:08", "status": "success", "message": "数据获取成功"}, "bkc.880709": {"sector_name": "虚拟数字人", "last_fetch_time": "2025-07-12 22:58:09", "status": "success", "message": "数据获取成功"}, "bkc.880713": {"sector_name": "DRG/DIP", "last_fetch_time": "2025-07-12 22:58:09", "status": "success", "message": "数据获取成功"}, "bkc.880714": {"sector_name": "低价股", "last_fetch_time": "2025-07-12 22:58:09", "status": "success", "message": "数据获取成功"}, "bkc.880719": {"sector_name": "动力电池回收", "last_fetch_time": "2025-07-12 22:58:09", "status": "success", "message": "数据获取成功"}, "bkc.880721": {"sector_name": "昨日连板_含一字", "last_fetch_time": "2025-07-12 22:58:10", "status": "success", "message": "数据获取成功"}, "bkc.880726": {"sector_name": "昨日涨停_含一字", "last_fetch_time": "2025-07-12 22:58:11", "status": "success", "message": "数据获取成功"}, "bkc.880731": {"sector_name": "EDR概念", "last_fetch_time": "2025-07-12 22:58:11", "status": "success", "message": "数据获取成功"}, "bkc.880732": {"sector_name": "IGBT概念", "last_fetch_time": "2025-07-12 22:58:11", "status": "success", "message": "数据获取成功"}, "bkc.880733": {"sector_name": "数据安全", "last_fetch_time": "2025-07-12 22:58:12", "status": "success", "message": "数据获取成功"}, "bkc.880738": {"sector_name": "调味品概念", "last_fetch_time": "2025-07-12 22:58:12", "status": "success", "message": "数据获取成功"}, "bkc.880743": {"sector_name": "预制菜概念", "last_fetch_time": "2025-07-12 22:58:12", "status": "success", "message": "数据获取成功"}, "bkc.880747": {"sector_name": "绿色电力", "last_fetch_time": "2025-07-12 22:58:13", "status": "success", "message": "数据获取成功"}, "bkc.880750": {"sector_name": "培育钻石", "last_fetch_time": "2025-07-12 22:58:13", "status": "success", "message": "数据获取成功"}, "bkc.880751": {"sector_name": "职业教育", "last_fetch_time": "2025-07-12 22:58:13", "status": "success", "message": "数据获取成功"}, "bkc.880753": {"sector_name": "发电机概念", "last_fetch_time": "2025-07-12 22:58:14", "status": "success", "message": "数据获取成功"}, "bkc.880757": {"sector_name": "华为欧拉", "last_fetch_time": "2025-07-12 22:58:14", "status": "success", "message": "数据获取成功"}, "bkc.880759": {"sector_name": "PVDF概念", "last_fetch_time": "2025-07-12 22:58:14", "status": "success", "message": "数据获取成功"}, "bkc.880764": {"sector_name": "环氧丙烷", "last_fetch_time": "2025-07-12 22:58:14", "status": "success", "message": "数据获取成功"}, "bkc.880767": {"sector_name": "磷化工", "last_fetch_time": "2025-07-12 22:58:15", "status": "success", "message": "数据获取成功"}, "bkc.880771": {"sector_name": "元宇宙概念", "last_fetch_time": "2025-07-12 22:58:16", "status": "success", "message": "数据获取成功"}, "bkc.880776": {"sector_name": "国资云概念", "last_fetch_time": "2025-07-12 22:58:16", "status": "success", "message": "数据获取成功"}, "bkc.880778": {"sector_name": "植物照明", "last_fetch_time": "2025-07-12 22:58:16", "status": "success", "message": "数据获取成功"}, "bkc.880779": {"sector_name": "碳基材料", "last_fetch_time": "2025-07-12 22:58:17", "status": "success", "message": "数据获取成功"}, "bkc.880780": {"sector_name": "专精特新", "last_fetch_time": "2025-07-12 22:58:17", "status": "success", "message": "数据获取成功"}, "bkc.880785": {"sector_name": "工业母机", "last_fetch_time": "2025-07-12 22:58:17", "status": "success", "message": "数据获取成功"}, "bkc.880787": {"sector_name": "抽水蓄能", "last_fetch_time": "2025-07-12 22:58:18", "status": "success", "message": "数据获取成功"}, "bkc.880788": {"sector_name": "激光雷达", "last_fetch_time": "2025-07-12 22:58:18", "status": "success", "message": "数据获取成功"}, "bkc.880792": {"sector_name": "内贸流通", "last_fetch_time": "2025-07-12 22:58:18", "status": "success", "message": "数据获取成功"}, "bkc.880793": {"sector_name": "宁组合", "last_fetch_time": "2025-07-12 22:58:19", "status": "success", "message": "数据获取成功"}, "bkc.880796": {"sector_name": "茅指数", "last_fetch_time": "2025-07-12 22:58:19", "status": "success", "message": "数据获取成功"}, "bkc.880800": {"sector_name": "机器视觉", "last_fetch_time": "2025-07-12 22:58:19", "status": "success", "message": "数据获取成功"}, "bkc.880803": {"sector_name": "NFT概念", "last_fetch_time": "2025-07-12 22:58:20", "status": "success", "message": "数据获取成功"}, "bkc.880804": {"sector_name": "毛发医疗", "last_fetch_time": "2025-07-12 22:58:20", "status": "success", "message": "数据获取成功"}, "bkc.880805": {"sector_name": "华为昇腾", "last_fetch_time": "2025-07-12 22:58:21", "status": "success", "message": "数据获取成功"}, "bkc.880809": {"sector_name": "空间站概念", "last_fetch_time": "2025-07-12 22:58:21", "status": "success", "message": "数据获取成功"}, "bkc.880814": {"sector_name": "宠物经济", "last_fetch_time": "2025-07-12 22:58:21", "status": "success", "message": "数据获取成功"}, "bkc.880816": {"sector_name": "REITs概念", "last_fetch_time": "2025-07-12 22:58:22", "status": "success", "message": "数据获取成功"}, "bkc.880821": {"sector_name": "工程机械概念", "last_fetch_time": "2025-07-12 22:58:22", "status": "success", "message": "数据获取成功"}, "bkc.880825": {"sector_name": "快递概念", "last_fetch_time": "2025-07-12 22:58:22", "status": "success", "message": "数据获取成功"}, "bkc.880829": {"sector_name": "储能", "last_fetch_time": "2025-07-12 22:58:23", "status": "success", "message": "数据获取成功"}, "bkc.880832": {"sector_name": "钠离子电池", "last_fetch_time": "2025-07-12 22:58:23", "status": "success", "message": "数据获取成功"}, "bkc.880836": {"sector_name": "CAR-T细胞疗法", "last_fetch_time": "2025-07-12 22:58:23", "status": "success", "message": "数据获取成功"}, "bkc.880839": {"sector_name": "换电概念", "last_fetch_time": "2025-07-12 22:58:24", "status": "success", "message": "数据获取成功"}, "bkc.880840": {"sector_name": "华为汽车", "last_fetch_time": "2025-07-12 22:58:24", "status": "success", "message": "数据获取成功"}, "bkc.880843": {"sector_name": "核污染防治", "last_fetch_time": "2025-07-12 22:58:24", "status": "success", "message": "数据获取成功"}, "bkc.880846": {"sector_name": "电子车牌", "last_fetch_time": "2025-07-12 22:58:25", "status": "success", "message": "数据获取成功"}, "bkc.880850": {"sector_name": "工业气体", "last_fetch_time": "2025-07-12 22:58:25", "status": "success", "message": "数据获取成功"}, "bkc.880855": {"sector_name": "化债(AMC)概念", "last_fetch_time": "2025-07-12 22:58:26", "status": "success", "message": "数据获取成功"}, "bkc.880856": {"sector_name": "低碳冶金", "last_fetch_time": "2025-07-12 22:58:26", "status": "success", "message": "数据获取成功"}, "bkc.880860": {"sector_name": "光伏建筑一体化", "last_fetch_time": "2025-07-12 22:58:26", "status": "success", "message": "数据获取成功"}, "bkc.880865": {"sector_name": "碳化硅", "last_fetch_time": "2025-07-12 22:58:27", "status": "success", "message": "数据获取成功"}, "bkc.880869": {"sector_name": "被动元件", "last_fetch_time": "2025-07-12 22:58:27", "status": "success", "message": "数据获取成功"}, "bkc.880870": {"sector_name": "磁悬浮概念", "last_fetch_time": "2025-07-12 22:58:27", "status": "success", "message": "数据获取成功"}, "bkc.880871": {"sector_name": "化妆品概念", "last_fetch_time": "2025-07-12 22:58:28", "status": "success", "message": "数据获取成功"}, "bkc.880874": {"sector_name": "注射器概念", "last_fetch_time": "2025-07-12 22:58:28", "status": "success", "message": "数据获取成功"}, "bkc.880876": {"sector_name": "快手概念", "last_fetch_time": "2025-07-12 22:58:28", "status": "success", "message": "数据获取成功"}, "bkc.880879": {"sector_name": "注册制次新股", "last_fetch_time": "2025-07-12 22:58:29", "status": "success", "message": "数据获取成功"}, "bkc.880881": {"sector_name": "生物识别", "last_fetch_time": "2025-07-12 22:58:29", "status": "success", "message": "数据获取成功"}, "bkc.880883": {"sector_name": "汽车芯片", "last_fetch_time": "2025-07-12 22:58:29", "status": "success", "message": "数据获取成功"}, "bkc.880886": {"sector_name": "固态电池", "last_fetch_time": "2025-07-12 22:58:30", "status": "success", "message": "数据获取成功"}, "bkc.880891": {"sector_name": "水产养殖", "last_fetch_time": "2025-07-12 22:58:30", "status": "success", "message": "数据获取成功"}, "bkc.880896": {"sector_name": "碳交易", "last_fetch_time": "2025-07-12 22:58:31", "status": "success", "message": "数据获取成功"}, "bkc.880900": {"sector_name": "社区团购", "last_fetch_time": "2025-07-12 22:58:31", "status": "success", "message": "数据获取成功"}, "bkc.880902": {"sector_name": "6G概念", "last_fetch_time": "2025-07-12 22:58:31", "status": "success", "message": "数据获取成功"}, "bkc.880904": {"sector_name": "商业航天", "last_fetch_time": "2025-07-12 22:58:32", "status": "success", "message": "数据获取成功"}, "bkc.880906": {"sector_name": "RCEP概念", "last_fetch_time": "2025-07-12 22:58:32", "status": "success", "message": "数据获取成功"}, "bkc.880908": {"sector_name": "有机硅", "last_fetch_time": "2025-07-12 22:58:32", "status": "success", "message": "数据获取成功"}, "bkc.880910": {"sector_name": "无线充电", "last_fetch_time": "2025-07-12 22:58:33", "status": "success", "message": "数据获取成功"}, "bkc.880913": {"sector_name": "数字阅读", "last_fetch_time": "2025-07-12 22:58:33", "status": "success", "message": "数据获取成功"}, "bkc.880918": {"sector_name": "虚拟电厂", "last_fetch_time": "2025-07-12 22:58:33", "status": "success", "message": "数据获取成功"}, "bkc.880923": {"sector_name": "拼多多概念", "last_fetch_time": "2025-07-12 22:58:34", "status": "success", "message": "数据获取成功"}, "bkc.880927": {"sector_name": "eSIM", "last_fetch_time": "2025-07-12 22:58:34", "status": "success", "message": "数据获取成功"}, "bkc.880929": {"sector_name": "C2M概念", "last_fetch_time": "2025-07-12 22:58:34", "status": "success", "message": "数据获取成功"}, "bkc.880931": {"sector_name": "盲盒经济", "last_fetch_time": "2025-07-12 22:58:35", "status": "success", "message": "数据获取成功"}, "bkc.880933": {"sector_name": "鸿蒙概念", "last_fetch_time": "2025-07-12 22:58:36", "status": "success", "message": "数据获取成功"}, "bkc.880938": {"sector_name": "第三代半导体", "last_fetch_time": "2025-07-12 22:58:36", "status": "success", "message": "数据获取成功"}, "bkc.880939": {"sector_name": "刀片电池", "last_fetch_time": "2025-07-12 22:58:36", "status": "success", "message": "数据获取成功"}, "bkc.880941": {"sector_name": "草甘膦", "last_fetch_time": "2025-07-12 22:58:37", "status": "success", "message": "数据获取成功"}, "bkc.880945": {"sector_name": "氦气概念", "last_fetch_time": "2025-07-12 22:58:37", "status": "success", "message": "数据获取成功"}, "bkc.880949": {"sector_name": "MicroLED", "last_fetch_time": "2025-07-12 22:58:37", "status": "success", "message": "数据获取成功"}, "bkc.880954": {"sector_name": "屏下摄像", "last_fetch_time": "2025-07-12 22:58:37", "status": "success", "message": "数据获取成功"}, "bkc.880958": {"sector_name": "EDA概念", "last_fetch_time": "2025-07-12 22:58:38", "status": "success", "message": "数据获取成功"}, "bkc.880962": {"sector_name": "装配建筑", "last_fetch_time": "2025-07-12 22:58:38", "status": "success", "message": "数据获取成功"}, "bkc.880964": {"sector_name": "肝素概念", "last_fetch_time": "2025-07-12 22:58:38", "status": "success", "message": "数据获取成功"}, "bkc.880966": {"sector_name": "汽车拆解", "last_fetch_time": "2025-07-12 22:58:39", "status": "success", "message": "数据获取成功"}, "bkc.880969": {"sector_name": "商汤概念", "last_fetch_time": "2025-07-12 22:58:39", "status": "success", "message": "数据获取成功"}, "bkc.880971": {"sector_name": "疫苗冷链", "last_fetch_time": "2025-07-12 22:58:39", "status": "success", "message": "数据获取成功"}, "bkc.880973": {"sector_name": "网红经济", "last_fetch_time": "2025-07-12 22:58:40", "status": "success", "message": "数据获取成功"}, "bkc.880974": {"sector_name": "辅助生殖", "last_fetch_time": "2025-07-12 22:58:41", "status": "success", "message": "数据获取成功"}, "bkc.880975": {"sector_name": "代糖概念", "last_fetch_time": "2025-07-12 22:58:41", "status": "success", "message": "数据获取成功"}, "bkc.880979": {"sector_name": "蚂蚁概念", "last_fetch_time": "2025-07-12 22:58:41", "status": "success", "message": "数据获取成功"}, "bkc.880980": {"sector_name": "长寿药", "last_fetch_time": "2025-07-12 22:58:42", "status": "success", "message": "数据获取成功"}, "bkc.880981": {"sector_name": "中芯概念", "last_fetch_time": "2025-07-12 22:58:42", "status": "success", "message": "数据获取成功"}, "bkc.880985": {"sector_name": "蝗虫防治", "last_fetch_time": "2025-07-12 22:58:42", "status": "success", "message": "数据获取成功"}, "bkc.880987": {"sector_name": "退税商店", "last_fetch_time": "2025-07-12 22:58:43", "status": "success", "message": "数据获取成功"}, "bkc.880988": {"sector_name": "尾气治理", "last_fetch_time": "2025-07-12 22:58:43", "status": "success", "message": "数据获取成功"}, "bkc.880992": {"sector_name": "地塞米松", "last_fetch_time": "2025-07-12 22:58:43", "status": "success", "message": "数据获取成功"}, "bkc.880994": {"sector_name": "抖音小店", "last_fetch_time": "2025-07-12 22:58:43", "status": "success", "message": "数据获取成功"}, "bkc.880998": {"sector_name": "免税概念", "last_fetch_time": "2025-07-12 22:58:44", "status": "success", "message": "数据获取成功"}, "bkc.881003": {"sector_name": "湖北自贸", "last_fetch_time": "2025-07-12 22:58:44", "status": "success", "message": "数据获取成功"}, "bkc.881005": {"sector_name": "北交所概念", "last_fetch_time": "2025-07-12 22:58:44", "status": "success", "message": "数据获取成功"}, "bkc.881007": {"sector_name": "地摊经济", "last_fetch_time": "2025-07-12 22:58:45", "status": "success", "message": "数据获取成功"}, "bkc.881008": {"sector_name": "抖音概念(字节概念)", "last_fetch_time": "2025-07-12 22:58:46", "status": "success", "message": "数据获取成功"}, "bkc.881009": {"sector_name": "数据中心", "last_fetch_time": "2025-07-12 22:58:46", "status": "success", "message": "数据获取成功"}, "bkc.881010": {"sector_name": "卫星互联网", "last_fetch_time": "2025-07-12 22:58:46", "status": "success", "message": "数据获取成功"}, "bkc.881015": {"sector_name": "车联网(车路云)", "last_fetch_time": "2025-07-12 22:58:47", "status": "success", "message": "数据获取成功"}, "bkc.881017": {"sector_name": "RCS概念", "last_fetch_time": "2025-07-12 22:58:47", "status": "success", "message": "数据获取成功"}, "bkc.881021": {"sector_name": "特高压", "last_fetch_time": "2025-07-12 22:58:47", "status": "success", "message": "数据获取成功"}, "bkc.881022": {"sector_name": "半导体概念", "last_fetch_time": "2025-07-12 22:58:48", "status": "success", "message": "数据获取成功"}, "bkc.881023": {"sector_name": "氮化镓", "last_fetch_time": "2025-07-12 22:58:48", "status": "success", "message": "数据获取成功"}, "bkc.881025": {"sector_name": "WiFi", "last_fetch_time": "2025-07-12 22:58:48", "status": "success", "message": "数据获取成功"}, "bkc.881029": {"sector_name": "医废处理", "last_fetch_time": "2025-07-12 22:58:49", "status": "success", "message": "数据获取成功"}, "bkc.881030": {"sector_name": "消毒剂", "last_fetch_time": "2025-07-12 22:58:49", "status": "success", "message": "数据获取成功"}, "bkc.881033": {"sector_name": "远程办公", "last_fetch_time": "2025-07-12 22:58:49", "status": "success", "message": "数据获取成功"}, "bkc.881034": {"sector_name": "口罩", "last_fetch_time": "2025-07-12 22:58:50", "status": "success", "message": "数据获取成功"}, "bkc.881035": {"sector_name": "降解塑料", "last_fetch_time": "2025-07-12 22:58:50", "status": "success", "message": "数据获取成功"}, "bkc.881038": {"sector_name": "HIT电池", "last_fetch_time": "2025-07-12 22:58:51", "status": "success", "message": "数据获取成功"}, "bkc.881043": {"sector_name": "转基因", "last_fetch_time": "2025-07-12 22:58:51", "status": "success", "message": "数据获取成功"}, "bkc.881044": {"sector_name": "流感", "last_fetch_time": "2025-07-12 22:58:51", "status": "success", "message": "数据获取成功"}, "bkc.881048": {"sector_name": "传感器", "last_fetch_time": "2025-07-12 22:58:52", "status": "success", "message": "数据获取成功"}, "bkc.881050": {"sector_name": "广电", "last_fetch_time": "2025-07-12 22:58:52", "status": "success", "message": "数据获取成功"}, "bkc.881053": {"sector_name": "云游戏", "last_fetch_time": "2025-07-12 22:58:52", "status": "success", "message": "数据获取成功"}, "bkc.881057": {"sector_name": "MiniLED", "last_fetch_time": "2025-07-12 22:58:53", "status": "success", "message": "数据获取成功"}, "bkc.881062": {"sector_name": "3D摄像头", "last_fetch_time": "2025-07-12 22:58:53", "status": "success", "message": "数据获取成功"}, "bkc.881065": {"sector_name": "新能源车", "last_fetch_time": "2025-07-12 22:58:53", "status": "success", "message": "数据获取成功"}, "bkc.881068": {"sector_name": "CRO", "last_fetch_time": "2025-07-12 22:58:54", "status": "success", "message": "数据获取成功"}, "bkc.881069": {"sector_name": "胎压监测", "last_fetch_time": "2025-07-12 22:58:54", "status": "success", "message": "数据获取成功"}, "bkc.881070": {"sector_name": "IPv6", "last_fetch_time": "2025-07-12 22:58:54", "status": "success", "message": "数据获取成功"}, "bkc.881071": {"sector_name": "白酒", "last_fetch_time": "2025-07-12 22:58:55", "status": "success", "message": "数据获取成功"}, "bkc.881072": {"sector_name": "维生素", "last_fetch_time": "2025-07-12 22:58:55", "status": "success", "message": "数据获取成功"}, "bkc.881073": {"sector_name": "阿兹海默", "last_fetch_time": "2025-07-12 22:58:56", "status": "success", "message": "数据获取成功"}, "bkc.881075": {"sector_name": "无线耳机", "last_fetch_time": "2025-07-12 22:58:56", "status": "success", "message": "数据获取成功"}, "bkc.881076": {"sector_name": "乳业", "last_fetch_time": "2025-07-12 22:58:56", "status": "success", "message": "数据获取成功"}, "bkc.881079": {"sector_name": "国产芯片", "last_fetch_time": "2025-07-12 22:58:57", "status": "success", "message": "数据获取成功"}, "bkc.881084": {"sector_name": "MLCC", "last_fetch_time": "2025-07-12 22:58:57", "status": "success", "message": "数据获取成功"}, "bkc.881088": {"sector_name": "医疗美容", "last_fetch_time": "2025-07-12 22:58:57", "status": "success", "message": "数据获取成功"}, "bkc.881090": {"sector_name": "农业种植", "last_fetch_time": "2025-07-12 22:58:58", "status": "success", "message": "数据获取成功"}, "bkc.881095": {"sector_name": "鸡肉概念", "last_fetch_time": "2025-07-12 22:58:58", "status": "success", "message": "数据获取成功"}, "bkc.881096": {"sector_name": "智慧政务", "last_fetch_time": "2025-07-12 22:58:58", "status": "success", "message": "数据获取成功"}, "bkc.881099": {"sector_name": "VPN", "last_fetch_time": "2025-07-12 22:58:59", "status": "success", "message": "数据获取成功"}, "bkc.881100": {"sector_name": "光刻机(胶)", "last_fetch_time": "2025-07-12 22:58:59", "status": "success", "message": "数据获取成功"}, "bkc.881102": {"sector_name": "数字货币", "last_fetch_time": "2025-07-12 22:58:59", "status": "success", "message": "数据获取成功"}, "bkc.881105": {"sector_name": "猪肉概念", "last_fetch_time": "2025-07-12 22:59:00", "status": "success", "message": "数据获取成功"}, "bkc.881109": {"sector_name": "3D玻璃", "last_fetch_time": "2025-07-12 22:59:00", "status": "success", "message": "数据获取成功"}, "bkc.881111": {"sector_name": "UWB概念", "last_fetch_time": "2025-07-12 22:59:01", "status": "success", "message": "数据获取成功"}, "bkc.881115": {"sector_name": "标准普尔", "last_fetch_time": "2025-07-12 22:59:01", "status": "success", "message": "数据获取成功"}, "bkc.881120": {"sector_name": "分拆预期", "last_fetch_time": "2025-07-12 22:59:01", "status": "success", "message": "数据获取成功"}, "bkc.881125": {"sector_name": "PCB", "last_fetch_time": "2025-07-12 22:59:02", "status": "success", "message": "数据获取成功"}, "bkc.881127": {"sector_name": "ETC", "last_fetch_time": "2025-07-12 22:59:03", "status": "success", "message": "数据获取成功"}, "bkc.881128": {"sector_name": "垃圾分类", "last_fetch_time": "2025-07-12 22:59:03", "status": "success", "message": "数据获取成功"}, "bkc.881129": {"sector_name": "青蒿素", "last_fetch_time": "2025-07-12 22:59:03", "status": "success", "message": "数据获取成功"}, "bkc.881133": {"sector_name": "单抗概念", "last_fetch_time": "2025-07-12 22:59:04", "status": "success", "message": "数据获取成功"}, "bkc.881138": {"sector_name": "GDR", "last_fetch_time": "2025-07-12 22:59:04", "status": "success", "message": "数据获取成功"}, "bkc.881141": {"sector_name": "富时罗素", "last_fetch_time": "2025-07-12 22:59:04", "status": "success", "message": "数据获取成功"}, "bkc.881143": {"sector_name": "人造肉", "last_fetch_time": "2025-07-12 22:59:05", "status": "success", "message": "数据获取成功"}, "bkc.881145": {"sector_name": "电子烟", "last_fetch_time": "2025-07-12 22:59:06", "status": "success", "message": "数据获取成功"}, "bkc.881149": {"sector_name": "氢能源", "last_fetch_time": "2025-07-12 22:59:06", "status": "success", "message": "数据获取成功"}, "bkc.881151": {"sector_name": "超级真菌", "last_fetch_time": "2025-07-12 22:59:06", "status": "success", "message": "数据获取成功"}, "bkc.881154": {"sector_name": "数字孪生", "last_fetch_time": "2025-07-12 22:59:07", "status": "success", "message": "数据获取成功"}, "bkc.881158": {"sector_name": "边缘计算", "last_fetch_time": "2025-07-12 22:59:07", "status": "success", "message": "数据获取成功"}, "bkc.881163": {"sector_name": "超清视频", "last_fetch_time": "2025-07-12 22:59:07", "status": "success", "message": "数据获取成功"}, "bkc.881167": {"sector_name": "工业大麻", "last_fetch_time": "2025-07-12 22:59:07", "status": "success", "message": "数据获取成功"}, "bkc.881172": {"sector_name": "纳米银", "last_fetch_time": "2025-07-12 22:59:08", "status": "success", "message": "数据获取成功"}, "bkc.881173": {"sector_name": "华为概念", "last_fetch_time": "2025-07-12 22:59:08", "status": "success", "message": "数据获取成功"}, "bkc.881176": {"sector_name": "电子竞技", "last_fetch_time": "2025-07-12 22:59:08", "status": "success", "message": "数据获取成功"}, "bkc.881178": {"sector_name": "冷链物流", "last_fetch_time": "2025-07-12 22:59:09", "status": "success", "message": "数据获取成功"}, "bkc.881183": {"sector_name": "纾困概念", "last_fetch_time": "2025-07-12 22:59:09", "status": "success", "message": "数据获取成功"}, "bkc.881187": {"sector_name": "进口博览", "last_fetch_time": "2025-07-12 22:59:09", "status": "success", "message": "数据获取成功"}, "bkc.881192": {"sector_name": "京东金融", "last_fetch_time": "2025-07-12 22:59:10", "status": "success", "message": "数据获取成功"}, "bkc.881197": {"sector_name": "影视概念", "last_fetch_time": "2025-07-12 22:59:11", "status": "success", "message": "数据获取成功"}, "bkc.881202": {"sector_name": "百度概念", "last_fetch_time": "2025-07-12 22:59:11", "status": "success", "message": "数据获取成功"}, "bkc.881203": {"sector_name": "天然气", "last_fetch_time": "2025-07-12 22:59:11", "status": "success", "message": "数据获取成功"}, "bkc.881207": {"sector_name": "富士康", "last_fetch_time": "2025-07-12 22:59:12", "status": "success", "message": "数据获取成功"}, "bkc.881209": {"sector_name": "体外诊断", "last_fetch_time": "2025-07-12 22:59:12", "status": "success", "message": "数据获取成功"}, "bkc.881213": {"sector_name": "OLED", "last_fetch_time": "2025-07-12 22:59:12", "status": "success", "message": "数据获取成功"}, "bkc.881214": {"sector_name": "知识产权", "last_fetch_time": "2025-07-12 22:59:12", "status": "success", "message": "数据获取成功"}, "bkc.881219": {"sector_name": "东北振兴", "last_fetch_time": "2025-07-12 22:59:13", "status": "success", "message": "数据获取成功"}, "bkc.881221": {"sector_name": "互联医疗", "last_fetch_time": "2025-07-12 22:59:13", "status": "success", "message": "数据获取成功"}, "bkc.881222": {"sector_name": "独角兽", "last_fetch_time": "2025-07-12 22:59:13", "status": "success", "message": "数据获取成功"}, "bkc.881227": {"sector_name": "乡村振兴", "last_fetch_time": "2025-07-12 22:59:14", "status": "success", "message": "数据获取成功"}, "bkc.881228": {"sector_name": "小米概念", "last_fetch_time": "2025-07-12 22:59:14", "status": "success", "message": "数据获取成功"}, "bkc.881229": {"sector_name": "工业互联", "last_fetch_time": "2025-07-12 22:59:14", "status": "success", "message": "数据获取成功"}, "bkc.881232": {"sector_name": "万达概念", "last_fetch_time": "2025-07-12 22:59:15", "status": "success", "message": "数据获取成功"}, "bkc.881237": {"sector_name": "区块链", "last_fetch_time": "2025-07-12 22:59:16", "status": "success", "message": "数据获取成功"}, "bkc.881238": {"sector_name": "新零售", "last_fetch_time": "2025-07-12 22:59:16", "status": "success", "message": "数据获取成功"}, "bkc.881239": {"sector_name": "养老金", "last_fetch_time": "2025-07-12 22:59:16", "status": "success", "message": "数据获取成功"}, "bkc.881240": {"sector_name": "租售同权", "last_fetch_time": "2025-07-12 22:59:17", "status": "success", "message": "数据获取成功"}, "bkc.881242": {"sector_name": "MSCI中国", "last_fetch_time": "2025-07-12 22:59:17", "status": "success", "message": "数据获取成功"}, "bkc.881246": {"sector_name": "壳资源", "last_fetch_time": "2025-07-12 22:59:17", "status": "success", "message": "数据获取成功"}, "bkc.881249": {"sector_name": "可燃冰", "last_fetch_time": "2025-07-12 22:59:18", "status": "success", "message": "数据获取成功"}, "bkc.881251": {"sector_name": "昨日触板", "last_fetch_time": "2025-07-12 22:59:18", "status": "success", "message": "数据获取成功"}, "bkc.881254": {"sector_name": "昨日连板", "last_fetch_time": "2025-07-12 22:59:18", "status": "success", "message": "数据获取成功"}, "bkc.881257": {"sector_name": "昨日涨停", "last_fetch_time": "2025-07-12 22:59:18", "status": "success", "message": "数据获取成功"}, "bkc.881262": {"sector_name": "大飞机", "last_fetch_time": "2025-07-12 22:59:19", "status": "success", "message": "数据获取成功"}, "bkc.881265": {"sector_name": "雄安新区", "last_fetch_time": "2025-07-12 22:59:19", "status": "success", "message": "数据获取成功"}, "bkc.881268": {"sector_name": "贬值受益", "last_fetch_time": "2025-07-12 22:59:19", "status": "success", "message": "数据获取成功"}, "bkc.881270": {"sector_name": "超级品牌", "last_fetch_time": "2025-07-12 22:59:20", "status": "success", "message": "数据获取成功"}, "bkc.881271": {"sector_name": "工业4.0", "last_fetch_time": "2025-07-12 22:59:21", "status": "success", "message": "数据获取成功"}, "bkc.881273": {"sector_name": "军民融合", "last_fetch_time": "2025-07-12 22:59:21", "status": "success", "message": "数据获取成功"}, "bkc.881276": {"sector_name": "共享经济", "last_fetch_time": "2025-07-12 22:59:21", "status": "success", "message": "数据获取成功"}, "bkc.881277": {"sector_name": "精准医疗", "last_fetch_time": "2025-07-12 22:59:22", "status": "success", "message": "数据获取成功"}, "bkc.881278": {"sector_name": "钛白粉", "last_fetch_time": "2025-07-12 22:59:22", "status": "success", "message": "数据获取成功"}, "bkc.881282": {"sector_name": "深股通", "last_fetch_time": "2025-07-12 22:59:22", "status": "success", "message": "数据获取成功"}, "bkc.881284": {"sector_name": "股权转让", "last_fetch_time": "2025-07-12 22:59:23", "status": "success", "message": "数据获取成功"}, "bkc.881288": {"sector_name": "无人驾驶", "last_fetch_time": "2025-07-12 22:59:23", "status": "success", "message": "数据获取成功"}, "bkc.881291": {"sector_name": "增强现实", "last_fetch_time": "2025-07-12 22:59:23", "status": "success", "message": "数据获取成功"}, "bkc.881292": {"sector_name": "人工智能", "last_fetch_time": "2025-07-12 22:59:24", "status": "success", "message": "数据获取成功"}, "bkc.881293": {"sector_name": "深证100R", "last_fetch_time": "2025-07-12 22:59:24", "status": "success", "message": "数据获取成功"}, "bkc.881295": {"sector_name": "创业板综", "last_fetch_time": "2025-07-12 22:59:24", "status": "success", "message": "数据获取成功"}, "bkc.881300": {"sector_name": "海绵城市", "last_fetch_time": "2025-07-12 22:59:25", "status": "success", "message": "数据获取成功"}, "bkc.881305": {"sector_name": "高送转", "last_fetch_time": "2025-07-12 22:59:25", "status": "success", "message": "数据获取成功"}, "bkc.881307": {"sector_name": "虚拟现实", "last_fetch_time": "2025-07-12 22:59:26", "status": "success", "message": "数据获取成功"}, "bkc.881308": {"sector_name": "PPP模式", "last_fetch_time": "2025-07-12 22:59:26", "status": "success", "message": "数据获取成功"}, "bkc.881310": {"sector_name": "健康中国", "last_fetch_time": "2025-07-12 22:59:26", "status": "success", "message": "数据获取成功"}, "bkc.881312": {"sector_name": "证金持股", "last_fetch_time": "2025-07-12 22:59:27", "status": "success", "message": "数据获取成功"}, "bkc.881317": {"sector_name": "北京冬奥", "last_fetch_time": "2025-07-12 22:59:27", "status": "success", "message": "数据获取成功"}, "bkc.881318": {"sector_name": "航母概念", "last_fetch_time": "2025-07-12 22:59:27", "status": "success", "message": "数据获取成功"}, "bkc.881321": {"sector_name": "5G概念", "last_fetch_time": "2025-07-12 22:59:28", "status": "success", "message": "数据获取成功"}, "bkc.881324": {"sector_name": "2025规划", "last_fetch_time": "2025-07-12 22:59:28", "status": "success", "message": "数据获取成功"}, "bkc.881327": {"sector_name": "一带一路", "last_fetch_time": "2025-07-12 22:59:28", "status": "success", "message": "数据获取成功"}, "bkc.881328": {"sector_name": "券商概念", "last_fetch_time": "2025-07-12 22:59:29", "status": "success", "message": "数据获取成功"}, "bkc.881332": {"sector_name": "量子科技", "last_fetch_time": "2025-07-12 22:59:29", "status": "success", "message": "数据获取成功"}, "bkc.881337": {"sector_name": "赛马概念", "last_fetch_time": "2025-07-12 22:59:29", "status": "success", "message": "数据获取成功"}, "bkc.881341": {"sector_name": "体育产业", "last_fetch_time": "2025-07-12 22:59:30", "status": "success", "message": "数据获取成功"}, "bkc.881346": {"sector_name": "沪股通", "last_fetch_time": "2025-07-12 22:59:30", "status": "success", "message": "数据获取成功"}, "bkc.881351": {"sector_name": "人脑工程", "last_fetch_time": "2025-07-12 22:59:31", "status": "success", "message": "数据获取成功"}, "bkc.881353": {"sector_name": "上证380", "last_fetch_time": "2025-07-12 22:59:31", "status": "success", "message": "数据获取成功"}, "bkc.881357": {"sector_name": "无人机", "last_fetch_time": "2025-07-12 22:59:31", "status": "success", "message": "数据获取成功"}, "bkc.881361": {"sector_name": "超级电容", "last_fetch_time": "2025-07-12 22:59:32", "status": "success", "message": "数据获取成功"}, "bkc.881362": {"sector_name": "中证500", "last_fetch_time": "2025-07-12 22:59:32", "status": "success", "message": "数据获取成功"}, "bkc.881366": {"sector_name": "充电桩", "last_fetch_time": "2025-07-12 22:59:32", "status": "success", "message": "数据获取成功"}, "bkc.881370": {"sector_name": "全息技术", "last_fetch_time": "2025-07-12 22:59:33", "status": "success", "message": "数据获取成功"}, "bkc.881375": {"sector_name": "免疫治疗", "last_fetch_time": "2025-07-12 22:59:33", "status": "success", "message": "数据获取成功"}, "bkc.881379": {"sector_name": "IPO受益", "last_fetch_time": "2025-07-12 22:59:33", "status": "success", "message": "数据获取成功"}, "bkc.881382": {"sector_name": "国产软件", "last_fetch_time": "2025-07-12 22:59:34", "status": "success", "message": "数据获取成功"}, "bkc.881384": {"sector_name": "小金属概念", "last_fetch_time": "2025-07-12 22:59:34", "status": "success", "message": "数据获取成功"}, "bkc.881387": {"sector_name": "基因测序", "last_fetch_time": "2025-07-12 22:59:34", "status": "success", "message": "数据获取成功"}, "bkc.881388": {"sector_name": "旅游概念", "last_fetch_time": "2025-07-12 22:59:35", "status": "success", "message": "数据获取成功"}, "bkc.881391": {"sector_name": "氟化工", "last_fetch_time": "2025-07-12 22:59:36", "status": "success", "message": "数据获取成功"}, "bkc.881393": {"sector_name": "阿里概念", "last_fetch_time": "2025-07-12 22:59:36", "status": "success", "message": "数据获取成功"}, "bkc.881396": {"sector_name": "举牌", "last_fetch_time": "2025-07-12 22:59:36", "status": "success", "message": "数据获取成功"}, "bkc.881400": {"sector_name": "京津冀", "last_fetch_time": "2025-07-12 22:59:37", "status": "success", "message": "数据获取成功"}, "bkc.881404": {"sector_name": "央国企改革", "last_fetch_time": "2025-07-12 22:59:37", "status": "success", "message": "数据获取成功"}, "bkc.881405": {"sector_name": "燃料电池", "last_fetch_time": "2025-07-12 22:59:37", "status": "success", "message": "数据获取成功"}, "bkc.881409": {"sector_name": "智能家居", "last_fetch_time": "2025-07-12 22:59:37", "status": "success", "message": "数据获取成功"}, "bkc.881410": {"sector_name": "超导概念", "last_fetch_time": "2025-07-12 22:59:38", "status": "success", "message": "数据获取成功"}, "bkc.881411": {"sector_name": "粤港自贸", "last_fetch_time": "2025-07-12 22:59:38", "status": "success", "message": "数据获取成功"}, "bkc.881412": {"sector_name": "独家药品", "last_fetch_time": "2025-07-12 22:59:38", "status": "success", "message": "数据获取成功"}, "bkc.881416": {"sector_name": "病毒防治", "last_fetch_time": "2025-07-12 22:59:39", "status": "success", "message": "数据获取成功"}, "bkc.881419": {"sector_name": "蓝宝石", "last_fetch_time": "2025-07-12 22:59:39", "status": "success", "message": "数据获取成功"}, "bkc.881421": {"sector_name": "沪企改革", "last_fetch_time": "2025-07-12 22:59:39", "status": "success", "message": "数据获取成功"}, "bkc.881423": {"sector_name": "彩票概念", "last_fetch_time": "2025-07-12 22:59:40", "status": "success", "message": "数据获取成功"}, "bkc.881425": {"sector_name": "生态农业", "last_fetch_time": "2025-07-12 22:59:41", "status": "success", "message": "数据获取成功"}, "bkc.881430": {"sector_name": "医疗器械概念", "last_fetch_time": "2025-07-12 22:59:41", "status": "success", "message": "数据获取成功"}, "bkc.881433": {"sector_name": "国家安防", "last_fetch_time": "2025-07-12 22:59:41", "status": "success", "message": "数据获取成功"}, "bkc.881436": {"sector_name": "苹果概念", "last_fetch_time": "2025-07-12 22:59:42", "status": "success", "message": "数据获取成功"}, "bkc.881441": {"sector_name": "电商概念", "last_fetch_time": "2025-07-12 22:59:42", "status": "success", "message": "数据获取成功"}, "bkc.881446": {"sector_name": "婴童概念", "last_fetch_time": "2025-07-12 22:59:42", "status": "success", "message": "数据获取成功"}, "bkc.881449": {"sector_name": "在线教育", "last_fetch_time": "2025-07-12 22:59:42", "status": "success", "message": "数据获取成功"}, "bkc.881454": {"sector_name": "智能电视", "last_fetch_time": "2025-07-12 22:59:43", "status": "success", "message": "数据获取成功"}, "bkc.881455": {"sector_name": "网络安全", "last_fetch_time": "2025-07-12 22:59:43", "status": "success", "message": "数据获取成功"}, "bkc.881457": {"sector_name": "养老概念", "last_fetch_time": "2025-07-12 22:59:43", "status": "success", "message": "数据获取成功"}, "bkc.881460": {"sector_name": "特斯拉", "last_fetch_time": "2025-07-12 22:59:44", "status": "success", "message": "数据获取成功"}, "bkc.881463": {"sector_name": "上海自贸", "last_fetch_time": "2025-07-12 22:59:44", "status": "success", "message": "数据获取成功"}, "bkc.881465": {"sector_name": "手游概念", "last_fetch_time": "2025-07-12 22:59:44", "status": "success", "message": "数据获取成功"}, "bkc.881467": {"sector_name": "智能穿戴", "last_fetch_time": "2025-07-12 22:59:45", "status": "success", "message": "数据获取成功"}, "bkc.881472": {"sector_name": "智能机器", "last_fetch_time": "2025-07-12 22:59:46", "status": "success", "message": "数据获取成功"}, "bkc.881473": {"sector_name": "创业成份", "last_fetch_time": "2025-07-12 22:59:46", "status": "success", "message": "数据获取成功"}, "bkc.881476": {"sector_name": "互联金融", "last_fetch_time": "2025-07-12 22:59:46", "status": "success", "message": "数据获取成功"}, "bkc.881479": {"sector_name": "B股", "last_fetch_time": "2025-07-12 22:59:47", "status": "success", "message": "数据获取成功"}, "bkc.881482": {"sector_name": "中超概念", "last_fetch_time": "2025-07-12 22:59:47", "status": "success", "message": "数据获取成功"}, "bkc.881483": {"sector_name": "大数据", "last_fetch_time": "2025-07-12 22:59:47", "status": "success", "message": "数据获取成功"}, "bkc.881484": {"sector_name": "土地流转", "last_fetch_time": "2025-07-12 22:59:47", "status": "success", "message": "数据获取成功"}, "bkc.881488": {"sector_name": "北斗导航", "last_fetch_time": "2025-07-12 22:59:48", "status": "success", "message": "数据获取成功"}, "bkc.881490": {"sector_name": "智慧城市", "last_fetch_time": "2025-07-12 22:59:48", "status": "success", "message": "数据获取成功"}, "bkc.881492": {"sector_name": "通用航空", "last_fetch_time": "2025-07-12 22:59:48", "status": "success", "message": "数据获取成功"}, "bkc.881494": {"sector_name": "海洋经济", "last_fetch_time": "2025-07-12 22:59:49", "status": "success", "message": "数据获取成功"}, "bkc.881497": {"sector_name": "地热能", "last_fetch_time": "2025-07-12 22:59:49", "status": "success", "message": "数据获取成功"}, "bkc.881498": {"sector_name": "3D打印", "last_fetch_time": "2025-07-12 22:59:49", "status": "success", "message": "数据获取成功"}, "bkc.881500": {"sector_name": "石墨烯", "last_fetch_time": "2025-07-12 22:59:50", "status": "success", "message": "数据获取成功"}, "bkc.881503": {"sector_name": "中药概念", "last_fetch_time": "2025-07-12 22:59:51", "status": "success", "message": "数据获取成功"}, "bkc.881505": {"sector_name": "食品安全", "last_fetch_time": "2025-07-12 22:59:51", "status": "success", "message": "数据获取成功"}, "bkc.881506": {"sector_name": "上证180_", "last_fetch_time": "2025-07-12 22:59:51", "status": "success", "message": "数据获取成功"}, "bkc.881507": {"sector_name": "上证50_", "last_fetch_time": "2025-07-12 22:59:51", "status": "success", "message": "数据获取成功"}, "bkc.881510": {"sector_name": "央视50_", "last_fetch_time": "2025-07-12 22:59:52", "status": "success", "message": "数据获取成功"}, "bkc.881512": {"sector_name": "油气设服", "last_fetch_time": "2025-07-12 22:59:52", "status": "success", "message": "数据获取成功"}, "bkc.881513": {"sector_name": "参股保险", "last_fetch_time": "2025-07-12 22:59:52", "status": "success", "message": "数据获取成功"}, "bkc.881515": {"sector_name": "页岩气", "last_fetch_time": "2025-07-12 22:59:53", "status": "success", "message": "数据获取成功"}, "bkc.881517": {"sector_name": "海工装备", "last_fetch_time": "2025-07-12 22:59:53", "status": "success", "message": "数据获取成功"}, "bkc.881519": {"sector_name": "参股新三板", "last_fetch_time": "2025-07-12 22:59:53", "status": "success", "message": "数据获取成功"}, "bkc.881522": {"sector_name": "水利建设", "last_fetch_time": "2025-07-12 22:59:54", "status": "success", "message": "数据获取成功"}, "bkc.881523": {"sector_name": "融资融券", "last_fetch_time": "2025-07-12 22:59:54", "status": "success", "message": "数据获取成功"}, "bkc.881525": {"sector_name": "风能", "last_fetch_time": "2025-07-12 22:59:54", "status": "success", "message": "数据获取成功"}, "bkc.881528": {"sector_name": "长江三角", "last_fetch_time": "2025-07-12 22:59:55", "status": "success", "message": "数据获取成功"}, "bkc.881531": {"sector_name": "铁路基建", "last_fetch_time": "2025-07-12 22:59:56", "status": "success", "message": "数据获取成功"}, "bkc.881534": {"sector_name": "太阳能", "last_fetch_time": "2025-07-12 22:59:56", "status": "success", "message": "数据获取成功"}, "bkc.881539": {"sector_name": "智能电网", "last_fetch_time": "2025-07-12 22:59:56", "status": "success", "message": "数据获取成功"}, "bkc.881540": {"sector_name": "LED", "last_fetch_time": "2025-07-12 22:59:57", "status": "success", "message": "数据获取成功"}, "bkc.881542": {"sector_name": "云计算", "last_fetch_time": "2025-07-12 22:59:57", "status": "success", "message": "数据获取成功"}, "bkc.881543": {"sector_name": "稀土永磁", "last_fetch_time": "2025-07-12 22:59:57", "status": "success", "message": "数据获取成功"}, "bkc.881544": {"sector_name": "核能核电", "last_fetch_time": "2025-07-12 22:59:58", "status": "success", "message": "数据获取成功"}, "bkc.881549": {"sector_name": "锂电池", "last_fetch_time": "2025-07-12 22:59:58", "status": "success", "message": "数据获取成功"}, "bkc.881552": {"sector_name": "预盈预增", "last_fetch_time": "2025-07-12 22:59:58", "status": "success", "message": "数据获取成功"}, "bkc.881553": {"sector_name": "预亏预减", "last_fetch_time": "2025-07-12 22:59:59", "status": "success", "message": "数据获取成功"}, "bkc.881558": {"sector_name": "深成500", "last_fetch_time": "2025-07-12 22:59:59", "status": "success", "message": "数据获取成功"}, "bkc.881562": {"sector_name": "股权激励", "last_fetch_time": "2025-07-12 22:59:59", "status": "success", "message": "数据获取成功"}, "bkc.881563": {"sector_name": "滨海新区", "last_fetch_time": "2025-07-12 22:59:59", "status": "success", "message": "数据获取成功"}, "bkc.881567": {"sector_name": "油价相关", "last_fetch_time": "2025-07-12 23:00:00", "status": "success", "message": "数据获取成功"}, "bkc.881571": {"sector_name": "基本金属", "last_fetch_time": "2025-07-12 23:00:01", "status": "success", "message": "数据获取成功"}, "bkc.881575": {"sector_name": "移动支付", "last_fetch_time": "2025-07-12 23:00:01", "status": "success", "message": "数据获取成功"}, "bkc.881580": {"sector_name": "物联网", "last_fetch_time": "2025-07-12 23:00:01", "status": "success", "message": "数据获取成功"}, "bkc.881583": {"sector_name": "机构重仓", "last_fetch_time": "2025-07-12 23:00:02", "status": "success", "message": "数据获取成功"}, "bkc.881584": {"sector_name": "深圳特区", "last_fetch_time": "2025-07-12 23:00:02", "status": "success", "message": "数据获取成功"}, "bkc.881585": {"sector_name": "生物疫苗", "last_fetch_time": "2025-07-12 23:00:02", "status": "success", "message": "数据获取成功"}, "bkc.881586": {"sector_name": "黄金概念", "last_fetch_time": "2025-07-12 23:00:03", "status": "success", "message": "数据获取成功"}, "bkc.881587": {"sector_name": "基金重仓", "last_fetch_time": "2025-07-12 23:00:03", "status": "success", "message": "数据获取成功"}, "bkc.881591": {"sector_name": "QFII重仓", "last_fetch_time": "2025-07-12 23:00:03", "status": "success", "message": "数据获取成功"}, "bkc.881592": {"sector_name": "成渝特区", "last_fetch_time": "2025-07-12 23:00:04", "status": "success", "message": "数据获取成功"}, "bkc.881595": {"sector_name": "转债标的", "last_fetch_time": "2025-07-12 23:00:04", "status": "success", "message": "数据获取成功"}, "bkc.881598": {"sector_name": "参股银行", "last_fetch_time": "2025-07-12 23:00:04", "status": "success", "message": "数据获取成功"}, "bkc.881601": {"sector_name": "参股期货", "last_fetch_time": "2025-07-12 23:00:05", "status": "success", "message": "数据获取成功"}, "bkc.881606": {"sector_name": "新材料", "last_fetch_time": "2025-07-12 23:00:05", "status": "success", "message": "数据获取成功"}, "bkc.881610": {"sector_name": "社保重仓", "last_fetch_time": "2025-07-12 23:00:06", "status": "success", "message": "数据获取成功"}, "bkc.881612": {"sector_name": "稀缺资源", "last_fetch_time": "2025-07-12 23:00:06", "status": "success", "message": "数据获取成功"}, "bkc.881616": {"sector_name": "参股券商", "last_fetch_time": "2025-07-12 23:00:07", "status": "success", "message": "数据获取成功"}, "bkc.881621": {"sector_name": "化工原料", "last_fetch_time": "2025-07-12 23:00:07", "status": "success", "message": "数据获取成功"}, "bkc.881623": {"sector_name": "ST股", "last_fetch_time": "2025-07-12 23:00:07", "status": "success", "message": "数据获取成功"}, "bkc.881624": {"sector_name": "网络游戏", "last_fetch_time": "2025-07-12 23:00:08", "status": "success", "message": "数据获取成功"}, "bkc.881626": {"sector_name": "创投", "last_fetch_time": "2025-07-12 23:00:08", "status": "success", "message": "数据获取成功"}, "bkc.881631": {"sector_name": "中字头", "last_fetch_time": "2025-07-12 23:00:08", "status": "success", "message": "数据获取成功"}, "bkc.881636": {"sector_name": "次新股", "last_fetch_time": "2025-07-12 23:00:09", "status": "success", "message": "数据获取成功"}, "bkc.881640": {"sector_name": "HS300_", "last_fetch_time": "2025-07-12 23:00:09", "status": "success", "message": "数据获取成功"}, "bkc.881645": {"sector_name": "AH股", "last_fetch_time": "2025-07-12 23:00:09", "status": "success", "message": "数据获取成功"}, "bkc.881650": {"sector_name": "AB股", "last_fetch_time": "2025-07-12 23:00:10", "status": "success", "message": "数据获取成功"}, "bkc.881655": {"sector_name": "节能环保", "last_fetch_time": "2025-07-12 23:00:10", "status": "success", "message": "数据获取成功"}, "bkc.881660": {"sector_name": "新能源", "last_fetch_time": "2025-07-12 23:00:11", "status": "success", "message": "数据获取成功"}, "bkc.881664": {"sector_name": "煤化工", "last_fetch_time": "2025-07-12 23:00:11", "status": "success", "message": "数据获取成功"}, "bkc.881668": {"sector_name": "军工", "last_fetch_time": "2025-07-12 23:00:11", "status": "success", "message": "数据获取成功"}, "bkc.881767": {"sector_name": "荣耀概念", "last_fetch_time": "2025-07-12 23:00:12", "status": "success", "message": "数据获取成功"}, "bkc.881768": {"sector_name": "AI眼镜", "last_fetch_time": "2025-07-12 23:00:12", "status": "success", "message": "数据获取成功"}, "bkc.881769": {"sector_name": "西部大开发", "last_fetch_time": "2025-07-12 23:00:12", "status": "success", "message": "数据获取成功"}, "bkc.881770": {"sector_name": "房屋检测", "last_fetch_time": "2025-07-12 23:00:13", "status": "success", "message": "数据获取成功"}, "bkc.881771": {"sector_name": "华为海思", "last_fetch_time": "2025-07-12 23:00:13", "status": "success", "message": "数据获取成功"}, "bkc.881772": {"sector_name": "并购重组概念", "last_fetch_time": "2025-07-12 23:00:13", "status": "success", "message": "数据获取成功"}, "bkc.881773": {"sector_name": "智谱AI", "last_fetch_time": "2025-07-12 23:00:14", "status": "success", "message": "数据获取成功"}, "bkc.881774": {"sector_name": "谷子经济", "last_fetch_time": "2025-07-12 23:00:14", "status": "success", "message": "数据获取成功"}, "bkc.881775": {"sector_name": "人形机器人", "last_fetch_time": "2025-07-12 23:00:14", "status": "success", "message": "数据获取成功"}, "bkc.881776": {"sector_name": "首发经济", "last_fetch_time": "2025-07-12 23:00:15", "status": "success", "message": "数据获取成功"}, "bkc.881777": {"sector_name": "冰雪经济", "last_fetch_time": "2025-07-12 23:00:15", "status": "success", "message": "数据获取成功"}, "bkc.881778": {"sector_name": "小红书概念", "last_fetch_time": "2025-07-12 23:00:16", "status": "success", "message": "数据获取成功"}, "bkc.881779": {"sector_name": "AI智能体", "last_fetch_time": "2025-07-12 23:00:16", "status": "success", "message": "数据获取成功"}, "bkc.881780": {"sector_name": "DeepSeek概念", "last_fetch_time": "2025-07-12 23:00:16", "status": "success", "message": "数据获取成功"}, "bkc.881781": {"sector_name": "腾讯云", "last_fetch_time": "2025-07-12 23:00:17", "status": "success", "message": "数据获取成功"}, "bkc.881782": {"sector_name": "虚拟机器人", "last_fetch_time": "2025-07-12 23:00:17", "status": "success", "message": "数据获取成功"}}