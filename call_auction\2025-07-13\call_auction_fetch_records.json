{"sh.600036": {"stock_name": "sh.600036", "last_fetch_time": "2025-07-13 10:56:24", "status": "success", "message": "数据获取成功"}, "sh.600519": {"stock_name": "sh.600519", "last_fetch_time": "2025-07-13 10:56:25", "status": "success", "message": "数据获取成功"}, "sz.000001": {"stock_name": "sz.000001", "last_fetch_time": "2025-07-13 10:56:27", "status": "success", "message": "数据获取成功"}, "sz.000002": {"stock_name": "sz.000002", "last_fetch_time": "2025-07-13 10:56:27", "status": "success", "message": "数据获取成功"}, "sh.600206": {"stock_name": "sh.600206", "last_fetch_time": "2025-07-13 10:56:30", "status": "success", "message": "数据获取成功"}, "sh.600000": {"stock_name": "sh.600000", "last_fetch_time": "2025-07-13 10:56:26", "status": "success", "message": "数据获取成功"}, "sz.000858": {"stock_name": "sz.000858", "last_fetch_time": "2025-07-13 10:56:29", "status": "success", "message": "数据获取成功"}, "sh.600276": {"stock_name": "sh.600276", "last_fetch_time": "2025-07-13 10:56:31", "status": "success", "message": "数据获取成功"}, "sh.600887": {"stock_name": "sh.600887", "last_fetch_time": "2025-07-13 10:56:32", "status": "success", "message": "数据获取成功"}, "sz.002415": {"stock_name": "sz.002415", "last_fetch_time": "2025-07-13 10:56:32", "status": "success", "message": "数据获取成功"}, "sz.300059": {"stock_name": "sz.300059", "last_fetch_time": "2025-07-13 10:56:34", "status": "success", "message": "数据获取成功"}, "sz.300750": {"stock_name": "sz.300750", "last_fetch_time": "2025-07-13 10:56:35", "status": "success", "message": "数据获取成功"}, "sh.601318": {"stock_name": "sh.601318", "last_fetch_time": "2025-07-13 10:56:36", "status": "success", "message": "数据获取成功"}, "sh.601398": {"stock_name": "sh.601398", "last_fetch_time": "2025-07-13 10:56:37", "status": "success", "message": "数据获取成功"}, "sh.601857": {"stock_name": "sh.601857", "last_fetch_time": "2025-07-13 10:56:38", "status": "success", "message": "数据获取成功"}}