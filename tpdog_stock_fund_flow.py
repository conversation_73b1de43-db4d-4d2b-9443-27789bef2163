import os
import requests
import json
import time
from datetime import datetime, time as dt_time
from dotenv import load_dotenv
import pandas as pd

# =============================================================================
# 配置参数
# =============================================================================
# 强制更新模式：True=忽略本地数据全量更新，False=启用增量更新（默认）
FORCE_UPDATE = False


# 注意：如果 FORCE_UPDATE = True，程序将忽略所有本地缓存，重新获取所有股票数据
#       这在以下情况下有用：
#       1. 需要获取最新的实时数据
#       2. 怀疑本地数据有问题需要重新获取
#       3. API数据结构发生变化需要重新获取
# =============================================================================


def load_tpdog_token():
    """
    加载TPDog Token从环境变量
    """
    # 加载 .env 文件中的环境变量
    load_dotenv()

    # 获取TPDOG_TOKEN
    token = os.getenv("TPDOG_TOKEN")

    if not token:
        print("❌ 错误: 未在 .env 文件中找到 TPDOG_TOKEN")
        print("   请在项目根目录创建 .env 文件，并添加: TPDOG_TOKEN=你的token")
        return None

    print(f"✅ 成功加载TPDog Token: {token[:10]}...")
    return token


def get_stock_list(token, stock_type="sz"):
    """
    获取股票列表

    参数:
    - token: TPDog API Token
    - stock_type: 交易所类型
      - sh: 上证
      - sz: 深证
      - bj: 北证
    """
    api_url = "https://www.tpdog.com/api/hs/stocks/list"

    params = {
        'type': stock_type,
        'token': token
    }

    try:
        print(f"🔄 正在获取{stock_type}股票列表...")
        response = requests.get(api_url, params=params, timeout=10)
        response.raise_for_status()

        data = response.json()

        if data.get("code") == 1000:
            content = data.get("content", [])
            print(f"✅ 成功获取 {len(content)} 只股票")
            return content
        else:
            print(f"❌ 获取股票列表失败: {data.get('message', '未知错误')}")
            return []

    except Exception as e:
        print(f"❌ 获取股票列表失败: {e}")
        return []


def is_after_market_close():
    """
    判断当前时间是否为收盘后（15点后）
    """
    current_time = datetime.now().time()
    market_close_time = dt_time(15, 0)  # 15:00
    return current_time >= market_close_time


def load_fetch_log(log_filepath):
    """
    加载获取记录日志

    参数:
    - log_filepath: 日志文件路径

    返回:
    - dict: 已获取的股票记录 {req_code: {timestamp, status, ...}}
    """
    if not os.path.exists(log_filepath):
        return {}

    try:
        with open(log_filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"⚠️  读取获取日志失败: {e}")
        return {}


def save_fetch_log(log_filepath, fetch_log):
    """
    保存获取记录日志

    参数:
    - log_filepath: 日志文件路径
    - fetch_log: 获取记录字典
    """
    try:
        with open(log_filepath, 'w', encoding='utf-8') as f:
            json.dump(fetch_log, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"❌ 保存获取日志失败: {e}")


def should_skip_stock(req_code, fetch_log, is_after_close):
    """
    判断是否应该跳过某只股票的获取

    参数:
    - req_code: 股票代码
    - fetch_log: 获取记录日志
    - is_after_close: 是否为收盘后时间

    返回:
    - bool: True表示跳过，False表示需要获取
    """
    # 如果开启强制更新模式，不跳过任何股票
    if FORCE_UPDATE:
        return False

    if not is_after_close:
        return False  # 非收盘后时间，不跳过任何股票

    if req_code not in fetch_log:
        return False  # 没有获取记录，不跳过

    stock_record = fetch_log[req_code]

    # 检查是否当天已成功获取
    if stock_record.get('status') == 'success':
        fetch_date = stock_record.get('fetch_date', '')
        today = datetime.now().strftime('%Y-%m-%d')

        if fetch_date == today:
            return True  # 当天已成功获取，跳过

    return False  # 其他情况不跳过
    """
    获取股票列表

    参数:
    - token: TPDog API Token
    - stock_type: 交易所类型
      - sh: 上证
      - sz: 深证
      - bj: 北证
    """
    api_url = "https://www.tpdog.com/api/hs/stocks/list"

    params = {
        'type': stock_type,
        'token': token
    }

    try:
        print(f"🔄 正在获取{stock_type}股票列表...")
        response = requests.get(api_url, params=params, timeout=10)
        response.raise_for_status()

        data = response.json()

        if data.get("code") == 1000:
            content = data.get("content", [])
            print(f"✅ 成功获取 {len(content)} 只股票")
            return content
        else:
            print(f"❌ 获取股票列表失败: {data.get('message', '未知错误')}")
            return []

    except Exception as e:
        print(f"❌ 获取股票列表失败: {e}")
        return []


def get_stock_fund_flow(token, req_code, date=None, period=1):
    """
    获取个股资金流数据

    参数:
    - token: TPDog API Token
    - req_code: 股票代码，格式如 sh.600206
    - date: 日期，格式 yyyy-MM-dd，默认为当前日期
    - period: 周期数，默认为1，取值范围[1-10]
    """

    # 如果没有指定日期，使用当前日期
    if date is None:
        date = datetime.now().strftime('%Y-%m-%d')

    # 构建API URL
    api_url = f"https://www.tpdog.com/api/hs/fund/stock"

    # 设置请求参数
    params = {
        'code': req_code,
        'date': date,
        'period': period,
        'token': token
    }

    try:
        # 发送GET请求
        response = requests.get(api_url, params=params, timeout=10)
        response.raise_for_status()

        # 解析JSON响应
        data = response.json()

        # 检查API响应状态
        if data.get("code") == 1000:
            return data.get("content", {})
        else:
            print(f"❌ 获取{req_code}资金流失败: {data.get('message', '未知错误')}")
            return None

    except Exception as e:
        print(f"❌ 获取{req_code}资金流失败: {e}")
        return None


def get_all_stocks_fund_flow(token, stocks, date=None, exchange_name=""):
    """
    获取所有股票的资金流数据，并实时保存，支持增量更新

    参数:
    - token: TPDog API Token
    - stocks: 股票列表
    - date: 日期
    - exchange_name: 交易所名称，用于文件命名
    """
    print(f"\n📊 开始获取所有股票资金流数据...")

    # 判断是否为收盘后时间
    is_after_close = is_after_market_close()
    current_time_str = datetime.now().strftime('%H:%M:%S')

    # 显示运行模式
    if FORCE_UPDATE:
        print(f"🔥 强制更新模式已启用，将重新获取所有股票数据")
    elif is_after_close:
        print(f"🕐 当前时间 {current_time_str}，已过收盘时间，启用增量更新模式")
    else:
        print(f"🕐 当前时间 {current_time_str}，交易时间内，获取所有股票数据")

    # 初始化保存目录
    now = datetime.now()
    date_folder = now.strftime('%Y-%m-%d')
    timestamp = now.strftime('%H-%M')
    fund_flow_dir = "fund_flow"
    date_dir = os.path.join(fund_flow_dir, date_folder)
    os.makedirs(date_dir, exist_ok=True)

    # 文件路径
    raw_data_filename = f"{timestamp}_tpdog_{exchange_name}_stock_raw_data.json"
    raw_data_filepath = os.path.join(date_dir, raw_data_filename)

    # 获取记录日志文件路径
    log_filename = f"tpdog_{exchange_name}_fetch_log.json"
    log_filepath = os.path.join(date_dir, log_filename)

    # 加载已有的获取记录
    fetch_log = load_fetch_log(log_filepath)

    all_data = []
    total_stocks = len(stocks)
    success_count = 0
    error_count = 0
    skipped_count = 0

    for i, stock in enumerate(stocks, 1):
        req_code = stock.get('req_code')
        stock_name = stock.get('name')
        stock_code = stock.get('code')

        # 检查是否需要跳过（在API请求之前判断）
        skip_this_stock = should_skip_stock(req_code, fetch_log, is_after_close)

        if skip_this_stock:
            skipped_count += 1
            print(f"⏭️  跳过 {stock_name}({stock_code}) - 当天已获取 ({i}/{total_stocks})")

            # 从之前的记录中恢复数据
            if req_code in fetch_log and fetch_log[req_code].get('data'):
                all_data.append(fetch_log[req_code]['data'])

            continue

        print(f"🔄 正在获取 {stock_name}({stock_code}) 资金流数据... ({i}/{total_stocks})")

        # 记录获取开始时间
        fetch_start_time = datetime.now()

        fund_data = get_stock_fund_flow(token, req_code, date)

        if fund_data:
            # 添加股票信息到数据中
            fund_data['stock_name'] = stock_name
            fund_data['stock_code'] = stock_code
            fund_data['req_code'] = req_code
            all_data.append(fund_data)
            success_count += 1

            # 更新获取记录日志
            fetch_log[req_code] = {
                'status': 'success',
                'fetch_date': now.strftime('%Y-%m-%d'),
                'fetch_time': fetch_start_time.strftime('%Y-%m-%d %H:%M:%S'),
                'stock_name': stock_name,
                'stock_code': stock_code,
                'data': fund_data  # 保存数据以支持跳过时恢复
            }

            # 实时保存到JSON文件
            try:
                with open(raw_data_filepath, 'w', encoding='utf-8') as f:
                    json.dump(all_data, f, ensure_ascii=False, indent=2)

                # 保存获取记录日志
                save_fetch_log(log_filepath, fetch_log)

                print(f"   💾 已保存第 {success_count} 只股票数据到文件")
            except Exception as e:
                print(f"   ❌ 保存数据失败: {e}")
        else:
            error_count += 1

            # 记录获取失败
            fetch_log[req_code] = {
                'status': 'failed',
                'fetch_date': now.strftime('%Y-%m-%d'),
                'fetch_time': fetch_start_time.strftime('%Y-%m-%d %H:%M:%S'),
                'stock_name': stock_name,
                'stock_code': stock_code,
                'error': 'API请求失败'
            }

            # 保存获取记录日志
            save_fetch_log(log_filepath, fetch_log)

            print(f"   ❌ 获取失败，继续下一只股票...")

        # 每获取100只股票显示一次进度
        if i % 100 == 0:
            print(
                f"📈 进度报告: 已处理 {i}/{total_stocks} 只股票，成功 {success_count} 只，失败 {error_count} 只，跳过 {skipped_count} 只")

        # 控制请求频率，避免触发限制（只有实际请求API时才需要等待）
        time.sleep(0.1)  # 等待100ms，确保不超过30次/秒的限制

    print(
        f"✅ 数据获取完成: 成功 {success_count} 只，失败 {error_count} 只，跳过 {skipped_count} 只，总计 {len(all_data)} 只股票的资金流数据")
    print(f"💾 原始数据已实时保存到: {raw_data_filepath}")
    print(f"📋 获取记录已保存到: {log_filepath}")

    return all_data


def create_stock_rankings(fund_flow_data):
    """
    创建个股资金流排行榜

    参数:
    - fund_flow_data: 所有股票的资金流数据列表
    """
    if not fund_flow_data:
        print("❌ 无数据可供排序")
        return

    # 转换为DataFrame便于排序
    df_data = []
    for data in fund_flow_data:
        df_data.append({
            '股票代码': data.get('code', 'N/A'),
            '股票名称': data.get('name', 'N/A'),
            '交易所': data.get('type', 'N/A'),
            '主力净流入': data.get('m_net', 0),
            '主力流入': data.get('m_in', 0),
            '主力流出': data.get('m_out', 0),
            '主力流入比例': data.get('m_in_ratio', 0),
            '主力流出比例': data.get('m_out_ratio', 0),
            '散户净流入': data.get('r_net', 0),
            '散户流入': data.get('r_in', 0),
            '散户流出': data.get('r_out', 0),
            '散户流入比例': data.get('r_in_ratio', 0),
            '散户流出比例': data.get('r_out_ratio', 0),
            '统计日期': data.get('start', 'N/A')
        })

    df = pd.DataFrame(df_data)

    # 1. 主力净流入排行榜（前100名）
    print("\n" + "=" * 100)
    print("🏆 个股主力资金净流入排行榜 TOP 100")
    print("=" * 100)

    net_inflow_top100 = df.nlargest(100, '主力净流入')

    print(
        f"{'排名':<4} {'股票代码':<10} {'股票名称':<15} {'主力净流入(元)':<15} {'流入比例':<8} {'流出比例':<8} {'交易所':<6}")
    print("-" * 100)

    for idx, (_, row) in enumerate(net_inflow_top100.iterrows(), 1):
        net_inflow = row['主力净流入']
        in_ratio = row['主力流入比例']
        out_ratio = row['主力流出比例']

        # 根据净流入金额选择显示颜色标识
        if net_inflow > 0:
            status = "✅"
        else:
            status = "❌"

        print(
            f"{idx:<4} {row['股票代码']:<10} {row['股票名称']:<15} {status} {net_inflow:>12,.0f} {in_ratio:>6.2f}% {out_ratio:>6.2f}% {row['交易所']:<6}")

    # 2. 主力流入比例排行榜（前100名）
    print("\n" + "=" * 100)
    print("📈 个股主力资金流入比例排行榜 TOP 100")
    print("=" * 100)

    inflow_ratio_top100 = df.nlargest(100, '主力流入比例')

    print(
        f"{'排名':<4} {'股票代码':<10} {'股票名称':<15} {'流入比例':<8} {'主力净流入(元)':<15} {'流出比例':<8} {'交易所':<6}")
    print("-" * 100)

    for idx, (_, row) in enumerate(inflow_ratio_top100.iterrows(), 1):
        net_inflow = row['主力净流入']
        in_ratio = row['主力流入比例']
        out_ratio = row['主力流出比例']

        if net_inflow > 0:
            status = "✅"
        else:
            status = "❌"

        print(
            f"{idx:<4} {row['股票代码']:<10} {row['股票名称']:<15} {in_ratio:>6.2f}% {status} {net_inflow:>12,.0f} {out_ratio:>6.2f}% {row['交易所']:<6}")

    # 3. 主力流入金额排行榜（前100名）
    print("\n" + "=" * 100)
    print("💰 个股主力资金流入金额排行榜 TOP 100")
    print("=" * 100)

    inflow_amount_top100 = df.nlargest(100, '主力流入')

    print(
        f"{'排名':<4} {'股票代码':<10} {'股票名称':<15} {'主力流入(元)':<15} {'流入比例':<8} {'净流入(元)':<15} {'交易所':<6}")
    print("-" * 100)

    for idx, (_, row) in enumerate(inflow_amount_top100.iterrows(), 1):
        inflow_amount = row['主力流入']
        in_ratio = row['主力流入比例']
        net_inflow = row['主力净流入']

        if net_inflow > 0:
            status = "✅"
        else:
            status = "❌"

        print(
            f"{idx:<4} {row['股票代码']:<10} {row['股票名称']:<15} {inflow_amount:>12,.0f} {in_ratio:>6.2f}% {status} {net_inflow:>12,.0f} {row['交易所']:<6}")

    # 4. 统计摘要
    print("\n" + "=" * 100)
    print("📊 个股资金流统计摘要")
    print("=" * 100)

    total_stocks = len(df)
    positive_net_inflow = len(df[df['主力净流入'] > 0])
    negative_net_inflow = len(df[df['主力净流入'] < 0])

    total_net_inflow = df['主力净流入'].sum()
    total_inflow = df['主力流入'].sum()
    total_outflow = df['主力流出'].sum()
    avg_inflow_ratio = df['主力流入比例'].mean()
    avg_outflow_ratio = df['主力流出比例'].mean()

    print(f"总股票数量: {total_stocks}")
    print(f"主力净流入股票: {positive_net_inflow} ({positive_net_inflow / total_stocks * 100:.1f}%)")
    print(f"主力净流出股票: {negative_net_inflow} ({negative_net_inflow / total_stocks * 100:.1f}%)")
    print(f"全市场主力净流入总额: {total_net_inflow:,.0f} 元")
    print(f"全市场主力流入总额: {total_inflow:,.0f} 元")
    print(f"全市场主力流出总额: {total_outflow:,.0f} 元")
    print(f"平均主力流入比例: {avg_inflow_ratio:.2f}%")
    print(f"平均主力流出比例: {avg_outflow_ratio:.2f}%")

    return df


def save_stock_data_to_files(df, fund_flow_data, exchange_name):
    """
    保存个股数据到fund_flow文件夹，按日期分类保存
    注意：原始数据已在获取过程中实时保存，这里主要保存排行榜文件
    """
    now = datetime.now()
    date_folder = now.strftime('%Y-%m-%d')
    timestamp = now.strftime('%H-%M')

    # 创建fund_flow文件夹和日期子文件夹
    fund_flow_dir = "fund_flow"
    date_dir = os.path.join(fund_flow_dir, date_folder)
    os.makedirs(date_dir, exist_ok=True)

    try:
        # 1. 保存完整排行榜CSV文件
        rankings_filename = f"{timestamp}_tpdog_{exchange_name}_stock_rankings.csv"
        rankings_filepath = os.path.join(date_dir, rankings_filename)
        df.to_csv(rankings_filepath, index=False, encoding='utf-8-sig')
        print(f"💾 完整排行榜数据已保存到: {rankings_filepath}")

        # 2. 保存主力净流入TOP100排行榜单独文件
        net_inflow_top100 = df.nlargest(100, '主力净流入')
        net_inflow_filename = f"{timestamp}_tpdog_{exchange_name}_stock_net_inflow_top100.csv"
        net_inflow_filepath = os.path.join(date_dir, net_inflow_filename)
        net_inflow_top100.to_csv(net_inflow_filepath, index=False, encoding='utf-8-sig')
        print(f"💾 净流入TOP100已保存到: {net_inflow_filepath}")

        # 3. 保存主力流入比例TOP100排行榜单独文件
        inflow_ratio_top100 = df.nlargest(100, '主力流入比例')
        inflow_ratio_filename = f"{timestamp}_tpdog_{exchange_name}_stock_inflow_ratio_top100.csv"
        inflow_ratio_filepath = os.path.join(date_dir, inflow_ratio_filename)
        inflow_ratio_top100.to_csv(inflow_ratio_filepath, index=False, encoding='utf-8-sig')
        print(f"💾 流入比例TOP100已保存到: {inflow_ratio_filepath}")

        # 4. 保存主力流入金额TOP100排行榜单独文件
        inflow_amount_top100 = df.nlargest(100, '主力流入')
        inflow_amount_filename = f"{timestamp}_tpdog_{exchange_name}_stock_inflow_amount_top100.csv"
        inflow_amount_filepath = os.path.join(date_dir, inflow_amount_filename)
        inflow_amount_top100.to_csv(inflow_amount_filepath, index=False, encoding='utf-8-sig')
        print(f"💾 流入金额TOP100已保存到: {inflow_amount_filepath}")

        # 5. 原始数据已在获取过程中实时保存，这里不再重复保存
        print(f"📄 原始数据已在获取过程中实时保存")

        # 6. 保存统计摘要文件
        summary_filename = f"{timestamp}_tpdog_{exchange_name}_stock_summary.txt"
        summary_filepath = os.path.join(date_dir, summary_filename)
        save_stock_summary_to_file(df, summary_filepath, exchange_name)
        print(f"💾 统计摘要已保存到: {summary_filepath}")

        print(f"\n📁 所有文件已保存到目录: {date_dir}")

    except Exception as e:
        print(f"❌ 保存文件失败: {e}")


def save_stock_summary_to_file(df, filepath, exchange_name):
    """
    保存个股统计摘要到文本文件
    """
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"TPDog {exchange_name}个股资金流统计摘要\n")
            f.write("=" * 50 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            total_stocks = len(df)
            positive_net_inflow = len(df[df['主力净流入'] > 0])
            negative_net_inflow = len(df[df['主力净流入'] < 0])

            total_net_inflow = df['主力净流入'].sum()
            total_inflow = df['主力流入'].sum()
            total_outflow = df['主力流出'].sum()
            avg_inflow_ratio = df['主力流入比例'].mean()
            avg_outflow_ratio = df['主力流出比例'].mean()

            f.write(f"总股票数量: {total_stocks}\n")
            f.write(f"主力净流入股票: {positive_net_inflow} ({positive_net_inflow / total_stocks * 100:.1f}%)\n")
            f.write(f"主力净流出股票: {negative_net_inflow} ({negative_net_inflow / total_stocks * 100:.1f}%)\n")
            f.write(f"全市场主力净流入总额: {total_net_inflow:,.0f} 元\n")
            f.write(f"全市场主力流入总额: {total_inflow:,.0f} 元\n")
            f.write(f"全市场主力流出总额: {total_outflow:,.0f} 元\n")
            f.write(f"平均主力流入比例: {avg_inflow_ratio:.2f}%\n")
            f.write(f"平均主力流出比例: {avg_outflow_ratio:.2f}%\n\n")

            # 添加前10名净流入、前10名流入比例、前10名流入金额
            f.write("主力净流入前10名:\n")
            f.write("-" * 40 + "\n")
            net_top10 = df.nlargest(10, '主力净流入')
            for idx, (_, row) in enumerate(net_top10.iterrows(), 1):
                f.write(f"{idx:2d}. {row['股票名称']:<10}({row['股票代码']}) {row['主力净流入']:>12,.0f} 元\n")

            f.write("\n主力流入比例前10名:\n")
            f.write("-" * 40 + "\n")
            ratio_top10 = df.nlargest(10, '主力流入比例')
            for idx, (_, row) in enumerate(ratio_top10.iterrows(), 1):
                f.write(f"{idx:2d}. {row['股票名称']:<10}({row['股票代码']}) {row['主力流入比例']:>6.2f}%\n")

            f.write("\n主力流入金额前10名:\n")
            f.write("-" * 40 + "\n")
            amount_top10 = df.nlargest(10, '主力流入')
            for idx, (_, row) in enumerate(amount_top10.iterrows(), 1):
                f.write(f"{idx:2d}. {row['股票名称']:<10}({row['股票代码']}) {row['主力流入']:>12,.0f} 元\n")

    except Exception as e:
        print(f"❌ 保存统计摘要失败: {e}")


def main():
    """
    主函数
    """
    print("🚀 TPDog 个股资金流数据获取工具启动...")

    # 1. 加载Token
    token = load_tpdog_token()
    if not token:
        return

    # 2. 选择交易所
    print("\n请选择交易所:")
    print("1. 上证 (sh)")
    print("2. 深证 (sz)")
    print("3. 北证 (bj)")
    print("4. 沪深两市 (sh + sz)")
    print("5. 全部交易所 (sh + sz + bj)")

    try:
        choice = input("请输入选择 (1-5): ").strip()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
        return

    # 根据选择获取股票列表
    if choice == "1":
        stock_types = [("sh", "上证")]
    elif choice == "2":
        stock_types = [("sz", "深证")]
    elif choice == "3":
        stock_types = [("bj", "北证")]
    elif choice == "4":
        stock_types = [("sh", "上证"), ("sz", "深证")]
    elif choice == "5":
        stock_types = [("sh", "上证"), ("sz", "深证"), ("bj", "北证")]
    else:
        print("❌ 无效选择，程序退出")
        return

    # 处理每个交易所
    for stock_type, exchange_name in stock_types:
        print(f"\n{'=' * 60}")
        print(f"🏛️  正在处理 {exchange_name} 交易所")
        print(f"{'=' * 60}")

        # 获取股票列表
        stocks = get_stock_list(token, stock_type)
        if not stocks:
            print(f"❌ 获取{exchange_name}股票列表失败")
            continue

        # 获取所有股票的资金流数据
        fund_flow_data = get_all_stocks_fund_flow(token, stocks, None, exchange_name)

        if fund_flow_data:
            # 创建并显示排行榜
            print(f"\n🔄 正在生成 {exchange_name} 排行榜...")
            df = create_stock_rankings(fund_flow_data)

            # 保存数据到文件
            if df is not None:
                print(f"\n🔄 正在保存 {exchange_name} 文件...")
                save_stock_data_to_files(df, fund_flow_data, exchange_name)
                print(f"✅ {exchange_name} 数据处理完成")
        else:
            print(f"❌ 未获取到{exchange_name}有效的资金流数据")

    print(f"\n{'=' * 60}")
    print("💡 提示:")
    print("   1. 数据来源于TPDog API，更新频率为30分钟")
    print("   2. 请确保在 .env 文件中正确设置了 TPDOG_TOKEN")
    print("   3. API限制为30次/秒，程序已自动控制请求频率")
    print("   4. 所有数据已按日期保存到 fund_flow 文件夹中")
    print("   5. 增量更新功能:")
    print("      - 15点后运行会自动跳过当天已获取的股票")
    print("      - 获取记录保存在 fetch_log.json 文件中")
    print("      - 支持断点续传，程序中断后可继续获取剩余数据")
    print("   6. 强制更新模式:")
    print(f"      - 当前设置: {'启用' if FORCE_UPDATE else '禁用'}")
    print("      - 修改文件头部 FORCE_UPDATE = True 可启用强制更新")
    print("      - 强制更新会忽略本地缓存，重新获取所有数据")
    print("   7. 保存的文件包括:")
    print("      - 完整排行榜数据 (CSV)")
    print("      - 净流入TOP100 (CSV)")
    print("      - 流入比例TOP100 (CSV)")
    print("      - 流入金额TOP100 (CSV)")
    print("      - 原始数据 (JSON)")
    print("      - 获取记录日志 (JSON)")
    print("      - 统计摘要 (TXT)")


if __name__ == "__main__":
    main()